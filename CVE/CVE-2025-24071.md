---
title: CVE-2025-24071_PoC
---

# CVE-2025-24071_PoC

CVE-2025-24071: NTLM Hash Leak via RAR/ZIP Extraction and .library-ms File

## Windows Explorer Automatically Initiates an SMB Authentication Request when a .library-ms File is Extracted from a .rar Archive, Leading to NTLM Hash Disclosure. The User Does not Need to Open or Execute the file—simply Extracting it is Enough to Trigger the Leak

### Blog post

[https://cti.monster/blog/2025/03/18/CVE-2025-24071.html](https://cti.monster/blog/2025/03/18/CVE-2025-24071.html)

# Usage

```bash

  

>>python poc.py

  

>>enter file name: your file name

  

>>enter IP: attacker IP

```

## Video

https://github.com/user-attachments/assets/fa6f16da-70ce-45e5-ac55-0c92a3623cad

# Update

## Update: Microsoft Has Changed Its CVE Number. The CVE Number Previously Defined by Microsoft, CVE-2025-24071, Has Been Updated to CVE-2025-24054.🤷‍♂️

![update](https://github.com/user-attachments/assets/2d789df7-489a-48b3-8f15-52ebb27c2063)
