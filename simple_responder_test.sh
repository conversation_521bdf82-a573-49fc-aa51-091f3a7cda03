#!/bin/bash

echo "[*] Simple Responder Test"
echo "[+] Killing any existing Responder processes"
sudo pkill -f responder 2>/dev/null || true
sleep 2

echo "[+] Creating test directory"
mkdir -p responder_test
cd responder_test

echo "[+] Current directory: $(pwd)"
echo "[+] Starting Responder manually..."
echo "[+] Command: sudo responder -I tun0 -wvF"
echo ""
echo "This will run Responder in the foreground."
echo "You can trigger your exploit from another terminal."
echo "Press Ctrl+C to stop."
echo ""

# Run responder in foreground so we can see output
sudo responder -I tun0 -wvF
