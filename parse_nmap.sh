#!/bin/bash

# Check for input
if [ -z "$1" ]; then
    echo "Usage: $0 <nmap_output_file>"
    exit 1
fi

INPUT_FILE="$1"

# Store current host
HOST=""

# Output header
echo "Host,Port,Protocol,State,Service,Version"

# Parse Nmap output
while read -r line; do
    # Match the Host/IP line
    if [[ $line =~ ^Nmap\ scan\ report\ for\ (.+)$ ]]; then
        HOST="${BASH_REMATCH[1]}"
    fi

    # Match open ports
    if [[ $line =~ ^([0-9]+)/([a-z]+)[[:space:]]+([a-z]+)[[:space:]]+([^\t]+)([[:space:]]+(.*))? ]]; then
        PORT="${BASH_REMATCH[1]}"
        PROTO="${BASH_REMATCH[2]}"
        STATE="${BASH_REMATCH[3]}"
        SERVICE="${BASH_REMATCH[4]}"
        VERSION="${BASH_REMATCH[6]}"
        echo "$HOST,$PORT,$PROTO,$STATE,$SERVICE,\"${VERSION}\""
    fi
done < "$INPUT_FILE"
