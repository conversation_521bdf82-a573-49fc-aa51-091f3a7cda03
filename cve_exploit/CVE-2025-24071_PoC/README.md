# CVE-2025-24071_PoC
CVE-2025-24071: NTLM Hash Leak via RAR/ZIP Extraction and .library-ms File



### Windows Explorer automatically initiates an SMB authentication request when a .library-ms file is extracted from a .rar archive, leading to NTLM hash disclosure. The user does not need to open or execute the file—simply extracting it is enough to trigger the leak.

#### blog post: 
[https://cti.monster/blog/2025/03/18/CVE-2025-24071.html](https://cti.monster/blog/2025/03/18/CVE-2025-24071.html)

## usage 

```bash

>>python poc.py

>>enter file name: your file name

>>enter IP: attacker IP
```

### video


https://github.com/user-attachments/assets/fa6f16da-70ce-45e5-ac55-0c92a3623cad

# update:
### Update: Microsoft has changed its CVE number. The CVE number previously defined by Microsoft, CVE-2025-24071, has been updated to CVE-2025-24054.🤷‍♂️
![update](https://github.com/user-attachments/assets/2d789df7-489a-48b3-8f15-52ebb27c2063)




