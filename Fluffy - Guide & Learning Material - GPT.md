---
title: <PERSON><PERSON><PERSON> - Guide & Learning Material - GPT
---

# Fluffy - Guide & Learning Material - GPT

Here’s a **step‑by‑step learning guide** for `HTB - Fluffy - Season 8 - Easy Machine`.

I’ve also included links to additional learning material and tools to support each step.

---

## 🐺 1. Enumeration

**Performed aggressive Nmap scan** for full TCP range and service detection:

- Identified key ports: 53, 139, 445 (SMB), 636/3269 (LDAPS), 5985 (WinRM), multiple RPC ports ([hyhforever.top](https://www.hyhforever.top/page/2/?utm_source=chatgpt.com "HYH's Blog - HYH的学习小站"), [1337sheets.com](https://www.1337sheets.com/p/htb-fluffy?utm_source=chatgpt.com "Hack The Box - HTB - Fluffy | 1337 Sheets")).
- Captured domain controller name: `DC01.fluffy.htb`.
- SSL cert info for LDAPS, confirming Active Directory.

**Configure hosts and Kerberos:**

- Add to `/etc/hosts`:
    `*********** DC01.fluffy.htb fluffy.htb DC01`

- Set `/etc/krb5.conf` with realm `FLUFFY.HTB`, KDC, and RC4-encryption settings ([1337sheets.com](https://www.1337sheets.com/p/htb-fluffy?utm_source=chatgpt.com "Hack The Box - HTB - Fluffy | 1337 Sheets")).

---

## 🛡️ 2. Exploitation of CVE‑2025‑24071 (Explorer Spoofing)

**Background:**
CVE‑2025‑24071 is a Windows File Explorer spoof vulnerability: Explorer automatically parses `.library‑ms` files and implicitly trusts SMB paths, enabling spoofing ([1337sheets.com](https://www.1337sheets.com/p/htb-fluffy?utm_source=chatgpt.com "Hack The Box - HTB - Fluffy | 1337 Sheets")).

**Steps:**

1. **Clone exploit repo:**
    `git clone https://github.com/ThemeHackers/CVE-2025-24071.git`

2. **Craft malicious `.library-ms` file** pointing to your SMB server.
3. **Host file on attacker SMB:** Serve exploit to victim.
4. **Convince user to open file in Windows Explorer.**
    This triggers automatic SMB connection and authentication.

5. **Capture NTLMv2 hash** using Responder or similar tool.

---

## 🎯 3. NTLMv2 Hash Capture & Relay

- **Set up Responder** on attack machine to capture incoming SMB auth attempts.
- **Trigger SMB auth** via the spoofed .library‑ms file.
- **Collect NTLMv2 hash.**

---

## 🧬 4. Abuse Certipy & Shadow Credentials

- Use **Certipy** with captured hash to authenticate to LDAP/LDAPS.
- Enumerate Active Directory certificates.
- Extract **Shadow Credentials** to escalate privileges up to a user capable of `Esc16` (certificate-based escalation).

---

## 🪜 5. ESC16 UPN Spoof -> Full Domain Admin

**ESC16** attack uses User Principal Name spoofing:

1. Create spoofed UPN certificate using Certipy.
2. Use it to authenticate as Domain Admin via LDAPS.
3. Obtain DC-level shell or domain admin privileges and within that, SYSTEM.

---

## 🖥️ 6. Root/Admin Shell

- Once authenticated as domain admin, execute remote commands to gain SYSTEM.
- Dump `NTDS.dit` or use WinRM/PsExec to obtain shell.

---

## 🚀 Step‑By‑Step Learning Guide

To master this flow, here are targeted learning resources:

|Skill Area|Resource|Why It’s Helpful|
|---|---|---|
|Active Directory fundamentals|[Microsoft Docs: AD Overview]|Understand DC, LDAPS, UPNs.|
|Nmap scanning|[Nmap Guide](https://nmap.org/book/man.html)|Deep dive into flags like `-sCV -T4`.|
|Kerberos & krb5.conf|[MIT Kerberos Tutorial]|Config Kerberos for RC4 ticket usage.|
|CVE‑2025‑24071|GitHub repo + CVE advisory|Details how the spoof works.|
|SMB poisoning|[Responder Tutorial]|For hash capture.|
|AD certificate abuse|[Certipy GitHub]|Use Certipy to enumerate certs & Shadow Cred.|
|ESC16 attack|[Red Team blog on ESC16]|Step-by-step UPN spoof and attack flow.|

---

## 🔗 Bonus Learning Tools

- **BloodHound** – Visualize AD attack paths.
- **Impacket** – Tools like `GetUserSPNs`, `smbexec`, etc.
- **Responder**, **Rubeus**, **Certipy** – For NTLMv2 capture, Kerberos, and certificate misuse.
- **Windows Sandbox or AD Lab** – Set up an isolated Active Directory for practice.

---

## 🎥 Walk‑Through Video

For a visual walkthrough, this latest YouTube guide on Fluffy is top-tier:

[Hack The Box: Fluffy Walk‑through (red‑teamer’s guide)](https://www.youtube.com/watch?v=SVIPtcxLZEE&utm_source=chatgpt.com)

---

## ✅ TL;DR Summary

1. **Recon:** Full port scan, SMB/LDAPS open, DC identified.
2. **CVE Exploit:** Use `.library‑ms` to trigger SMB auth.
3. **Hash Capture:** Responder traps NTLMv2.
4. **Cert Abuse:** Use Certipy for cert enumeration/shadow cred.
5. **ESC16 Attack:** UPN spoof -> Domain Admin.
6. **Access:** Use admin creds to get SYSTEM.

---

## ⏭️ What to Practically Do

- **Setup a lab**: Domain Controller + a Windows user.
- **Reproduce the exploit**: Use provided repo and Responder.
- **Practice enumeration**: Build proficiency with BloodHound, Impacket, AD tools.
- **Follow video guide**: Reinforce steps visually and hands-on.

With this guide, you get both a solid translation of HYH's methodology and a clear path to mastering the techniques involved. Let me know if you want help setting up a lab environment for practice!
